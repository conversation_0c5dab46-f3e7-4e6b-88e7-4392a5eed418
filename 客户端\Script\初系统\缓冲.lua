-- @Author: baidwwy
-- @Date:   2024-11-29 19:11:39
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-13 13:58:23

local 无边框启动器 = require("Script/初系统/无边框启动器")

-- 启动配置
local 启动配置 = {
    引擎帧率 = 60,
    淡入时长 = 0.4, -- 淡入时长（秒）
    帧延迟 = 16, -- 约60FPS的帧延迟
    默认宽度 = 800,
    默认高度 = 600,
    -- 低配置优化选项
    低配置模式 = false, -- 可通过配置文件控制
    低配置帧延迟 = 33, -- 约30FPS，减少CPU占用
    简化动画 = false -- 简化启动动画效果
}

-- 检测系统配置
local function 检测系统配置()
    -- 简单的系统配置检测
    local 内存信息 = collectgarbage("count")
    local 低配置阈值 = 50000 -- 50MB内存使用阈值

    -- 如果内存使用较高或者用户设置了低配置模式
    if 内存信息 > 低配置阈值 or 启动配置.低配置模式 then
        启动配置.低配置模式 = true
        启动配置.简化动画 = true
        print("检测到低配置环境，启用优化模式")
    end
end

local function 主启动流程()
    -- 检测系统配置
    检测系统配置()

    -- 根据配置决定是否使用无边框启动器
    local 前置成功 = false
    if not 启动配置.低配置模式 then
        前置成功 = 无边框启动器.启动器()
    end

    if 前置成功 then
        -- 前置启动器已完成，现在恢复正常窗口样式
        local 游戏宽度 = 全局游戏宽度 or 启动配置.默认宽度
        local 游戏高度 = 全局游戏高度 or 启动配置.默认高度

        -- 获取窗口句柄
        local 窗口句柄 = 引擎.取窗口句柄()

        -- 恢复正常窗口样式
        无边框启动器.恢复窗口(窗口句柄, 游戏宽度, 游戏高度)

        -- 设置引擎分辨率
        引擎.置宽高(游戏宽度, 游戏高度)

        -- 设置窗口标题
        引擎.置标题(标题.."  当前版本v"..版本号.."   版本更新群:834248191")



        -- 添加淡入效果，与启动器的淡出形成完美过渡
        local ffi = require("ffi")

        if 启动配置.简化动画 then
            -- 简化版淡入效果，减少渲染次数
            local 简化步数 = 5
            for i = 1, 简化步数 do
                local 透明度 = math.floor(255 * (i / 简化步数))
                local 淡入背景 = ARGB(透明度, 0, 0, 0)

                引擎.渲染开始()
                引擎.渲染清除(淡入背景)
                引擎.渲染结束()

                ffi.C.Sleep(启动配置.低配置帧延迟)
            end
        else
            -- 原版平滑淡入效果
            local 淡入开始 = os.clock()
            local 当前帧延迟 = 启动配置.低配置模式 and 启动配置.低配置帧延迟 or 启动配置.帧延迟

            while true do
                local 淡入进度 = (os.clock() - 淡入开始) / 启动配置.淡入时长
                if 淡入进度 >= 1.0 then
                    break
                end

                -- 平滑的透明度曲线 (缓入效果)
                local 缓动进度 = math.pow(淡入进度, 2) -- 缓入效果
                local 透明度 = math.floor(255 * 缓动进度)
                local 淡入背景 = ARGB(透明度, 0, 0, 0)

                引擎.渲染开始()
                引擎.渲染清除(淡入背景)
                引擎.渲染结束()

                ffi.C.Sleep(当前帧延迟)
            end
        end
    else
        -- 如果前置启动器失败或低配置模式，使用标准方式创建引擎
        local 游戏宽度 = 全局游戏宽度 or 启动配置.默认宽度
        local 游戏高度 = 全局游戏高度 or 启动配置.默认高度
        local 引擎帧率 = 启动配置.低配置模式 and 30 or 启动配置.引擎帧率 -- 低配置模式降低帧率

        引擎(标题.."  当前版本v"..版本号.."   版本更新群:834248191", 游戏宽度, 游戏高度, 引擎帧率, true, true, true)

        if 启动配置.低配置模式 then
            print("低配置模式：使用标准启动方式，帧率限制为30FPS")
        end
    end
end

-- 执行主启动流程
主启动流程()




-- 热更新相关函数

-- 获取更新服务器地址
local function 获取更新服务器()
    if 调试模式 then
        return "http://127.0.0.1"
    else
        return "http://**************"
    end
end

-- 创建批处理文件执行更新
function 创建更新批处理(zip_file)
    local bat_content = [[
@echo off
title 梦幻西游游戏更新
color 0A
echo ================================================
echo               梦幻西游游戏更新程序
echo ================================================
echo 正在更新游戏，请稍候...
echo.

:: 设置命令执行出错时不退出批处理
setlocal enabledelayedexpansion

:: 检查进程是否已关闭
echo 检查游戏进程状态...
:check_process
tasklist | find /i "g2d.exe" >nul 2>&1
if not errorlevel 1 (
    echo 等待游戏进程关闭...
    timeout /t 1 >nul
    goto check_process
)
echo 游戏进程已关闭，准备开始更新。
echo.

:: 备份原始更新文件，以防更新失败后需要重试
echo 备份更新文件...
copy "]] .. zip_file .. [[" "]] .. zip_file .. [[.bak" >nul 2>&1

:: 创建临时目录用于解压
if not exist "temp_update" mkdir temp_update

:: 尝试使用不同的解压工具
set EXTRACT_SUCCESS=0

:: 1. 尝试使用7z.exe
if exist "7z.exe" (
    echo 使用7z.exe解压更新文件...
    start /wait 7z.exe x -y -aoa "]] .. zip_file .. [[" -o.\ >nul 2>&1
    if !errorlevel! equ 0 set EXTRACT_SUCCESS=1
) else if exist "tools\7z.exe" (
    echo 使用tools\7z.exe解压更新文件...
    start /wait tools\7z.exe x -y -aoa "]] .. zip_file .. [[" -o.\ >nul 2>&1
    if !errorlevel! equ 0 set EXTRACT_SUCCESS=1
)

:: 2. 尝试使用WinRAR
if !EXTRACT_SUCCESS!==0 (
    if exist "C:\Program Files\WinRAR\WinRAR.exe" (
        echo 使用WinRAR解压更新文件...
        start /wait "WinRAR" "C:\Program Files\WinRAR\WinRAR.exe" x -y "]] .. zip_file .. [[" .\ >nul 2>&1
        if !errorlevel! equ 0 set EXTRACT_SUCCESS=1
    ) else if exist "C:\Program Files (x86)\WinRAR\WinRAR.exe" (
        echo 使用WinRAR解压更新文件...
        start /wait "WinRAR" "C:\Program Files (x86)\WinRAR\WinRAR.exe" x -y "]] .. zip_file .. [[" .\ >nul 2>&1
        if !errorlevel! equ 0 set EXTRACT_SUCCESS=1
    )
)

:: 3. 尝试使用Windows内置的展开命令
if !EXTRACT_SUCCESS!==0 (
    echo 使用Windows内置解压工具...
    powershell -NoProfile -ExecutionPolicy Bypass -Command "& {try { Expand-Archive -Path ']] .. zip_file .. [[' -DestinationPath '.' -Force -ErrorAction Stop; exit 0 } catch { exit 1 }}" >nul 2>&1
    if !errorlevel! equ 0 set EXTRACT_SUCCESS=1
)

:: 4. 尝试使用tar命令(Windows 10 1803及更高版本支持)
if !EXTRACT_SUCCESS!==0 (
    echo 使用tar命令解压更新文件...
    tar -xf "]] .. zip_file .. [[" >nul 2>&1
    if !errorlevel! equ 0 set EXTRACT_SUCCESS=1
)

:: 检查解压是否成功
if !EXTRACT_SUCCESS!==0 (
    echo 解压失败，请手动解压更新文件
    echo 更新文件位置: %CD%\]] .. zip_file .. [[

    :: 创建一个提示文件，告知用户需要手动解压
    echo 游戏更新失败，请手动解压 %CD%\]] .. zip_file .. [[ 到游戏目录。 > 更新失败请手动解压.txt

    :: 尝试打开文件夹，方便用户找到更新文件
    explorer %CD%

    :: 启动游戏并显示错误消息框
    start "" g2d.exe

    :: 直接退出批处理
    exit
)

echo 更新完成，正在启动游戏...

:: 直接启动游戏并立即退出批处理
start "" g2d.exe
exit
]]

    -- 写入批处理文件
    local bat_file = "update.bat"
    local f = io.open(bat_file, "w")
    if not f then
        return false
    end
    f:write(bat_content)
    f:close()
    return true
end

-- 简单下载文件并显示进度
function 简单_下载文件(url_to_download, file_name)
    local cURL = require("lcurl")

    -- 显示下载开始提示
    if tp and tp.提示 then
        tp.提示:写入("#Y开始下载更新文件...")
    end

    -- 打开文件准备写入
    local f, err = io.open(file_name, "w+b")
    if not f then
        if tp and tp.提示 then
            tp.提示:写入("#R无法创建文件: " .. (err or "未知错误"))
        end
        return nil
    end

    -- 下载进度变量
    local 上次进度 = 0

    -- 创建cURL对象
    local url = cURL.easy()
        :setopt_url(url_to_download)
        :setopt(cURL.OPT_NOPROGRESS, false)
        :setopt_writefunction(f)
        :setopt_progressfunction(function(dltotal, dlnow, ultotal, ulnow)
            -- 显示下载进度
            if dltotal > 0 then
                local 当前进度 = math.floor(dlnow / dltotal * 100)
                if 当前进度 >= 上次进度 + 10 then  -- 每增加10%显示一次
                    上次进度 = 当前进度
                    if tp and tp.提示 then
                        tp.提示:写入(string.format("#G下载进度: %d%%", 当前进度))
                    end
                end
            end
        end)

    -- 设置超时
    url:setopt(cURL.OPT_CONNECTTIMEOUT, 10)
    url:setopt(cURL.OPT_TIMEOUT, 300)  -- 5分钟超时

    -- 执行下载
    local success, err = url:perform()
    url:close()
    f:close()

    -- 处理下载结果
    if not success then
        if tp and tp.提示 then
            tp.提示:写入("#R下载失败: " .. (err or "未知错误"))
        end
        return nil
    end

    -- 下载完成提示
    if tp and tp.提示 then
        tp.提示:写入("#G更新文件下载完成")
    end

    -- 读取文件内容
    local file = io.open(file_name, "r")
    if not file then
        return nil
    end

    local content = file:read("*a")
    file:close()
    return content
end

-- 从服务器获取版本号
function 从服务器获取版本号()
    local 更新服务器 = 获取更新服务器()
    local version_content = 简单_下载文件(更新服务器.."/version.txt?t="..os.time(), "version.txt")

    if not version_content then
        return nil
    end

    return tonumber(version_content)
end

-- 检测热更版本号
function 检测热更版本号()
    if 调试模式 then
        return false
    end

    local local_version = 版本号  -- 本地版本号
    local server_version = 从服务器获取版本号()  -- 从服务器获取当前的版本号

    if not server_version then
        if tp and tp.提示 then
            tp.提示:写入("#Y无法连接更新服务器，请检查网络")
        end
        return false
    end

    -- 返回是否需要更新和服务器版本号
    return local_version < server_version, server_version
end

-- 下载更新文件
function 下载更新文件()
    local 更新服务器 = 获取更新服务器()
    return 简单_下载文件(更新服务器.."/g2d.zip?t="..os.time(), "g2d.zip") ~= nil
end

-- 执行自动更新
function 执行自动更新()
    -- 检查是否需要更新
    local 需要更新, 服务器版本 = 检测热更版本号()

    if not 需要更新 then
        return false
    end

    -- 显示更新提示
    if tp and tp.提示 then
        tp.提示:写入(string.format("#Y发现新版本 v%s，正在准备更新...", 服务器版本))
    end

    -- 下载更新文件
    if not 下载更新文件() then
        if tp and tp.提示 then
            tp.提示:写入("#R更新文件下载失败，请稍后重试")
        end
        return false
    end

    -- 创建更新批处理文件
    if 创建更新批处理("g2d.zip") then
        -- 提示用户更新将在游戏关闭后自动进行
        f函数.信息框("更新文件已下载完成，点击确定后将自动更新游戏。\n\n游戏将关闭，系统将尝试使用多种解压工具完成更新，更新完成后会自动重启。", "版本更新")

        -- 启动批处理文件
        os.execute("start update.bat")

        -- 关闭游戏
        引擎.关闭()
        return true
    else
        -- 创建批处理失败，提示手动更新
        f函数.信息框("无法创建自动更新程序，请手动解压g2d.zip覆盖当前文件。", "版本更新")
        引擎.关闭()
        return true
    end
end

-- 执行更新检查
if not 调试模式 then  -- 只有非调试模式才执行更新检查
    执行自动更新()
end

