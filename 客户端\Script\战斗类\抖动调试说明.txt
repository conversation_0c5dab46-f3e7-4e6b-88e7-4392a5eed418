抖动调试信息说明
================

为了调试法术抖动问题，已在以下位置添加了打印语句：

1. 战斗类.lua - 设置法术抖动开关时：
   - 执行流程201.1：打印"【抖动调试】战斗流程201.1 - 准备设置单位X的法术抖动，技能：XXX"
   - 执行流程206.1：打印"【抖动调试】战斗流程206.1 - 准备设置单位X的法术抖动，技能：XXX"
   - 执行流程907：打印"【抖动调试】战斗流程907 - 准备设置单位X的法术抖动，技能：XXX"

2. 战斗单位类.lua - 法术受击抖动函数中：
   - 开始新抖动时：打印"【抖动调试】单位X开始新的法术抖动，技能：XXX"
   - 完全无需抖动时：打印"【抖动调试】技能XXX完全无需抖动"
   - 使用延迟设置时：打印延迟帧数
   - 抖动过程中：每10帧打印一次当前状态
   - 达到最大时间时：打印"【抖动调试】单位X达到最大抖动时间，强制停止抖动"

3. 战斗单位类.lua - 回合结束重置函数中：
   - 如果抖动未正常结束：打印警告并强制清理

调试步骤：
1. 运行游戏并进入战斗
2. 使用龙卷雨击等法术攻击
3. 查看控制台输出，观察抖动的开始、进行和结束过程
4. 如果出现"警告！单位X在回合结束时法术抖动开关仍然存在"，说明有问题

正常情况下的输出顺序应该是：
1. "准备设置单位X的法术抖动"
2. "单位X开始新的法术抖动"
3. "使用XXX表的延迟：Y帧"
4. "单位X抖动中...持续时间：Z/120"（每10帧一次）
5. 抖动正常结束，没有警告信息

如果需要关闭调试信息，搜索所有包含"【抖动调试】"的print语句并注释掉即可。 