-- @Author: baidwwy
-- @Date:   2024-05-13 00:08:03
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-13 13:28:16
local 战斗类 = class()
local yxs = require("script/资源类/FSB")
local mousea = 引擎.鼠标按下
local mouseb = 引擎.鼠标弹起
local jtus = 引擎.截图到纹理
local jl1 = require("gge精灵类")
local function 排序(a,b) return a.显示y < b.显示y end
function 战斗类:初始化(根)
	local jl = require("gge精灵类")
	local 资源 = 根.资源
	local 按钮 = require("script/系统类/按钮")
	self:加载阵法()
	self.纯色背景 = jl(0,0,0,全局游戏宽度+500,全局游戏高度+300)
	self.纯色背景:置颜色(ARGB(160,3,20,70))
	self.黑幕背景 = jl(0,0,0,全局游戏宽度+500,全局游戏高度+300)
	self.黑幕背景:置颜色(0xFF000000)
	self.蓝色背景 = jl(0,0,0,全局游戏宽度+500,全局游戏高度+300)
	self.蓝色背景:置颜色(-1189208494)
	self.红色背景 = jl(0,0,0,全局游戏宽度+500,全局游戏高度+300)
	self.红色背景:置颜色(-2189208494)
	self.战斗背景 = 资源:载入('wzife.wdf',"网易WDF动画",0x00D17553)
	self.背景状态 = 0
	self.回合进程="加载"
	self.加载数量=0
	self.战斗单位={}
	self.回合数 = 0
	self.战斗单位[ljcs]={}
	self.能量条 = 资源:载入('wzife.wdf',"网易WDF动画",0xD34CC9FE)
	self.能量条背景 = 资源:载入('wzife.wdf',"网易WDF动画",0x74602246)
	self.进程="等待"
	self.自动开关=true
	self.天地洞明=false
	self.单挑模式=nil
	self.PK战斗=nil
	self.数字图片=引擎.场景.战斗文字[5]
	if 引擎.场景.观战中 == false then
		self.请等待=资源:载入('wzife.wd1',"网易WDF动画",0xEE1713AC)
	else
		self.请等待=资源:载入('wzife.wdf',"网易WDF动画",4254597813)
	end
	local wz = require("gge文字类")
	self.战斗信息提示={开关=false,内容="",起始时间=0,停止时间=0,字体=wz.创建(nil,16,false,false,false),}
	self.战斗信息提示.字体:置颜色(0xFFE6DB74)
	self.拼接偏移 = 生成XY()
	self.法宝图片 = 引擎.场景.战斗文字[1]
	self.战斗快捷键法术=nil
	self.宠物战斗快捷键法术=nil
	self.拼接特效={}
	self.显示排序={}
	self.回合数 = 0
	self.特殊状态 = {}
	self.无间地狱 = nil
	self.色变动画我方 = nil
	self.色变我方 = nil
	self.媚眼如丝 = nil
	self.法阵能量=nil
	self.过度纹理 = require("gge纹理类")():渲染目标(全局游戏宽度,全局游戏高度)
	self.我方头像组={}
	self.敌方头像组={}
	self.怪物头像组={}
	self.我方头像开关=false
	self.敌方头像开关=false
	self.我方阵型=nil
	self.敌方阵型=nil

	self.技能展示开关=false
	self.技能展示={}
	self.技能待放组={
	[1] = 资源:载入('wzife.wdf',"网易WDF动画",0x52CAD7EE),
	[2] = 资源:载入('wzife.wdf',"网易WDF动画",0x52F42754),
	[3] = 资源:载入("pic/zddt.png","图片"),
	[4] = 资源:载入('wzife.wdf',"网易WDF动画",0x60AAAFDE),
	[5] = 资源:载入('wzife.wdf',"网易WDF动画",0XEACCCBE2),
	}
end
function 战斗类:加载战斗资源()
	local 资源 = tp.资源
	local 按钮 = require("script/系统类/按钮")
	self.血条背景 = 资源:载入('wzife.wdf',"网易WDF动画",0x4D0A334C)
	self.血条栏 = 资源:载入('wzife.wdf',"网易WDF动画",0x4FD9FFF3)
	self.血条上限栏=资源:载入('magic.wdf',"网易WDF动画",3805549259)
	self.自动栏 = require("script/战斗类/战斗自动栏").创建(tp)
	self.观战栏 = require("script/战斗类/战斗观战").创建(tp)
	self.竖条 = 按钮(tp.资源:载入('wzife.wdf',"网易WDF动画",399414929),0,0,1)
	self.横条 = 按钮(tp.资源:载入('wzife.wdf',"网易WDF动画",0xe03a06b3),0,0,1)
	self.技能圈 = 资源:载入("pic/11.png","图片")
	self.战斗指令=战斗指令类(tp)
	self.阵型组={
	[1]=资源:载入('wzife.wdf',"网易WDF动画",0X140BBA9),
	[2]=资源:载入("pic/zf.png","图片"),
	[3]=资源:载入('wzife.wdf',"网易WDF动画",0xEACCCBE2),
	[4]=资源:载入('wzife.wdf',"网易WDF动画",0x9F3FDBA6),
	[5]=资源:载入('wzife.wdf',"网易WDF动画",0x8787A405),
	[6]=资源:载入('wzife.wdf',"网易WDF动画",0x439709D9),
	[7]=资源:载入('wzife.wdf',"网易WDF动画",0x7485AA08),
	[8]=资源:载入("pic/tcbj.png","图片"),
	}
end
function 战斗类:进入战斗过度()
	if not self.纯色背景 then
		local jl = require("gge精灵类")
		self.纯色背景 = jl(0,0,0,全局游戏宽度+500,全局游戏高度+300)
		self.纯色背景:置颜色(ARGB(160,3,20,70))
		self.过度纹理 = require("gge纹理类")():渲染目标(全局游戏宽度,全局游戏高度)
	end

	jtus(self.过度纹理)
	self.过度精灵 = jl1(self.过度纹理)
	self.过度精灵1 = jl1(self.过度纹理)
	self.过度精灵1:置区域(0,0,全局游戏宽度/2,全局游戏高度/2)
	self.过度精灵2 = jl1(self.过度纹理)
	self.过度精灵2:置区域(全局游戏宽度/2,0,全局游戏宽度/2,全局游戏高度/2)
	self.过度精灵3 = jl1(self.过度纹理)
	self.过度精灵3:置区域(0,全局游戏宽度/2,全局游戏宽度/2,全局游戏高度/2)
	self.过度精灵4 = jl1(self.过度纹理)
	self.过度精灵4:置区域(全局游戏宽度/2,全局游戏高度/2,全局游戏宽度/2,全局游戏高度/2)
	self.过度坐标1 = {x=0,y=0}
	self.过度坐标2 = {x=全局游戏宽度/2,y=0}
	self.过度坐标3 = {x=0,y=全局游戏高度/2}
	self.过度坐标4 = {x = 全局游戏宽度/2,y = 全局游戏高度/2}
	self.过度进度 = 255
	self.过度时间 = 100
end
function 战斗类:退出战斗过度()
	jtus(引擎.场景.场景.过度纹理)
	引擎.场景.场景.过度精灵 = jl1(引擎.场景.场景.过度纹理)
	引擎.场景.场景.过度进度 = 255
	引擎.场景.场景.过度时间 = 3
	引擎.场景.场景.过度减少 = 5
end

function 战斗类:释放()
	self.进程="等待"
	self.加载数量=0
	for i=1,#self.战斗单位[ljcs] do
	 self.战斗单位[ljcs][i]:释放()
	end
	self.天地洞明=false
	self.战斗单位[ljcs]=nil
	self.战斗流程={}
	self.显示排序={}
	self.拼接特效 = {}
	self.回合数 = 0
	self.特殊状态 = {}
	self.无间地狱 = nil
	self.无间地狱死亡 = nil
	self.无间地狱开关 = nil
	self.清静菩提 = nil
	self.清静菩提动画 = nil
	self.清静菩提开关 = nil
	self.媚眼如丝 = nil
	self.媚眼如丝回血 = nil
	self.媚眼如丝开关 = nil
	self.色变动画我方 = nil
	self.色变我方 = nil
	self.色变进度我方 = nil
	self.色变动画敌方 = nil
	self.色变敌方 = nil
	self.色变进度敌方 = nil
	self.背景状态 = nil
	self.单挑模式=nil
	self.PK战斗=nil
	self.我方头像组={}
	self.敌方头像组={}
	self.怪物头像组={}
	self.技能展示={}
	self.我方阵型=nil
	self.敌方阵型=nil
	self.我方头像开关=false
	self.敌方头像开关=false

end
function 战斗类:加载怪物头像(单位,编号)
	if 单位.敌我==1 then
	else
		local x = 引擎.取头像(单位.模型)
		if x[1]==nil then
			x = 引擎.取头像("泡泡")
		end
		self.怪物头像组[#self.怪物头像组+1]=tp._按钮.创建(tp.资源:载入(x[7],"网易WDF动画",x[1]),0,0,1)
		self.怪物头像组[#self.怪物头像组].num=编号
		self.怪物头像组[#self.怪物头像组].敌我=单位.敌我
		self.怪物头像组[#self.怪物头像组].名称=单位.数据.名称
		self.怪物头像组[#self.怪物头像组].等级=单位.数据.等级
		self.怪物头像组[#self.怪物头像组].气血=单位.数据.气血
		self.怪物头像组[#self.怪物头像组].最大气血=单位.数据.最大气血
		self.怪物头像组[#self.怪物头像组].伤害=单位.数据.伤害
		self.怪物头像组[#self.怪物头像组].法伤=单位.数据.法伤
		self.怪物头像组[#self.怪物头像组].防御=单位.数据.防御
		self.怪物头像组[#self.怪物头像组].法防=单位.数据.法防
		self.怪物头像组[#self.怪物头像组].速度=单位.数据.速度
	end
end
function 战斗类:刷新怪物数据(单位,编号)
	if 单位.敌我==1 then
	else
		self.怪物头像组[#self.怪物头像组].num=编号
		self.怪物头像组[#self.怪物头像组].敌我=单位.敌我
		self.怪物头像组[#self.怪物头像组].名称=单位.数据.名称
		self.怪物头像组[#self.怪物头像组].等级=单位.数据.等级
		self.怪物头像组[#self.怪物头像组].气血=单位.数据.气血
		self.怪物头像组[#self.怪物头像组].最大气血=单位.数据.最大气血
		self.怪物头像组[#self.怪物头像组].伤害=单位.数据.伤害
		self.怪物头像组[#self.怪物头像组].法伤=单位.数据.法伤
		self.怪物头像组[#self.怪物头像组].防御=单位.数据.防御
		self.怪物头像组[#self.怪物头像组].法防=单位.数据.法防
		self.怪物头像组[#self.怪物头像组].速度=单位.数据.速度
	end
end
function 战斗类:设置命令回合(数据)
	--self.回合数 = self.回合数 +1
	self.战斗指令:加载()
	-- self.自动栏:加载()
	self.战斗指令:更新类型(数据)
	self.进程="命令"
	self.命令数据={计时=时间,分=4,秒=2}
	if self.PK战斗 and not self.单挑模式 then
		for k,v in pairs(self.技能展示) do
			local x = 引擎.取技能展示("等待")
			local mx = tp.资源:载入(x[6],"网易WDF动画",x[7])
			self.技能展示[k]={名称="等待中",小模型 = mx,偏移=x[10]}
		end
	end
end
function 战斗类:设置自动数据(数据)
    -- 按照队伍位置排序
    local 临时数据 = {}
    for i, v in ipairs(数据) do
        if v[1] then -- 有角色数据
            table.insert(临时数据, {
                位置 = v[1].位置,
                数据 = v
            })
        end
    end
    -- 按位置排序
    table.sort(临时数据, function(a, b)
        return a.位置 < b.位置
    end)
    -- 保存排序后的数据
    self.自动数据 = {}
    for i, v in ipairs(临时数据) do
        self.自动数据[i] = v.数据
    end
end
function 战斗类:是否开启自动()
	for n=1,#self.战斗单位[ljcs] do
		if self.战斗单位[ljcs][n].数据.自动战斗 and self.战斗单位[ljcs][n].数据.类型=="角色" and self.战斗单位[ljcs][n].数据.id==引擎.场景.队伍[1].数字id then
			self.自动开关=true
		end
	end
end

function 战斗类:加载单位(数据)
	self.战斗单位[ljcs][#self.战斗单位[ljcs]+1]=战斗单位类()
	self.战斗单位[ljcs][#self.战斗单位[ljcs]]:创建单位(数据,self.队伍id,#self.战斗单位[ljcs])
	if #self.战斗单位[ljcs]==self.单位总数 then
		if 引擎.场景.观战中 == false then
			发送数据(5501)
		end
		self:是否开启自动()
	end
	self.法阵能量 = 数据.法阵能量
	if self.我方阵型==nil then
		if self.战斗单位[ljcs][#self.战斗单位[ljcs]].敌我==1 then
			self.我方阵型=self.战斗单位[ljcs][#self.战斗单位[ljcs]].数据.附加阵法
			local 资源 = 引擎.取阵法效果(self.我方阵型)
			self.我方阵法 = tp.资源:载入(资源[2],"网易WDF动画",资源[1])
		end
	end
	if self.敌方阵型==nil then
		if self.战斗单位[ljcs][#self.战斗单位[ljcs]].敌我==2 then
			self.敌方阵型=self.战斗单位[ljcs][#self.战斗单位[ljcs]].数据.附加阵法
			资源 = 引擎.取阵法效果(self.敌方阵型)
			self.敌方阵法 = tp.资源:载入(资源[2],"网易WDF动画",资源[1])
		end
	end
	if  not self.单挑模式 then
		if self.战斗单位[ljcs][#self.战斗单位[ljcs]].敌我==1 then
			local x = 引擎.取技能展示("等待")
			local mx = tp.资源:载入(x[6],"网易WDF动画",x[7])
			self.技能展示[self.战斗单位[ljcs][#self.战斗单位[ljcs]].数据.位置]={名称="等待中",小模型 = mx,偏移=x[10]}
		end
		self:加载单位头像(self.战斗单位[ljcs][#self.战斗单位[ljcs]],#self.战斗单位[ljcs])
	end
	self:加载怪物头像(self.战斗单位[ljcs][#self.战斗单位[ljcs]],#self.战斗单位[ljcs])
end
function 战斗类:加载单位头像(单位,编号)
	if 单位.数据.类型=="角色" then
		if 单位.敌我==1 then
			local x = 引擎.取头像(单位.模型)
			self.我方头像组[#self.我方头像组+1]=tp._按钮.创建(tp.资源:载入(x[7],"网易WDF动画",x[2]),0,0,1)
			self.我方头像组[#self.我方头像组].num=编号
			self.我方头像组[#self.我方头像组].敌我=单位.敌我
			self.我方头像组[#self.我方头像组].名称=单位.数据.名称
			self.我方头像组[#self.我方头像组].门派=单位.数据.门派
			self.我方头像组[#self.我方头像组].等级=单位.数据.等级
		else
			local x = 引擎.取头像(单位.模型)
			self.敌方头像组[#self.敌方头像组+1]=tp._按钮.创建(tp.资源:载入(x[7],"网易WDF动画",x[2]),0,0,1)
			self.敌方头像组[#self.敌方头像组].num=编号
			self.敌方头像组[#self.敌方头像组].敌我=单位.敌我
			self.敌方头像组[#self.敌方头像组].名称=单位.数据.名称
			self.敌方头像组[#self.敌方头像组].门派=单位.数据.门派
			self.敌方头像组[#self.敌方头像组].等级=单位.数据.等级
		end
	end
end
function 战斗类:设置战斗流程(内容)
	self.战斗流程=内容

	-- 如果是断线重连，直接接受服务端的时间和状态
	if 内容.断线重连 then
		self.战斗时间 = 内容.战斗时间
		-- 不进行时间验证，直接处理战斗流程
		for n=1,#self.战斗流程 do
			if self.战斗流程~=nil and self.战斗流程[n]~=nil then
				self.战斗流程[n].执行=false
				self.战斗流程[n].允许=false
			end
		end
		if #self.战斗流程==0 then
			self.进程="等待"
			return
		end
		self.回合数 = self.回合数 + 1
		self.进程="计算"
		return
	end

	-- 正常验证流程
	if not 引擎.场景.观战中  and  not self.战斗流程[1] and  引擎.场景.观战中 == false  then
		写配置("./config.ini","mhxy","封禁原因","跳过封号1")
		发送数据(5825.1,{"跳过流程"})
		return
	end
	if not 引擎.场景.观战中  and   not 内容.战斗时间 and  引擎.场景.观战中 == false  then
		写配置("./config.ini","mhxy","封禁原因2","跳过封号2")
		发送数据(5825.1)
		return
	end
	-- 增加时间验证的容忍度，从10秒增加到60秒
	if not 引擎.场景.观战中  and   内容.战斗时间~=self.战斗时间  and math.abs(内容.战斗时间 - self.战斗时间)>60 then
		写配置("./config.ini","mhxy","封禁原因3",内容.战斗时间)
		写配置("./config.ini","mhxy","封禁原因4",self.战斗时间)
		发送数据(5825.1)
		return
	end
	for n=1,#self.战斗流程 do
		if self.战斗流程~=nil and self.战斗流程[n]~=nil then
			self.战斗流程[n].执行=false
			self.战斗流程[n].允许=false
		end
	end
	if #self.战斗流程==0 then
		self.进程="等待"
		if 引擎.场景.观战中 == false then
			发送数据(5825.1,{self.战斗时间})
		end
		return
	end
	self.回合数 = self.回合数 +1

	-- 回合开始时刷新怪物头像数据
	for i=1, #self.战斗单位[ljcs] do
		if self.战斗单位[ljcs][i].敌我 == 2 then
			self:刷新怪物头像数据(self.战斗单位[ljcs][i], i)
		end
	end

	self.进程="计算"
end

-- 添加一个专门刷新怪物头像数据的函数
function 战斗类:刷新怪物头像数据(单位, 编号)
	if not 单位 or 单位.敌我 ~= 2 then
		return
	end

	-- 找到对应的怪物头像
	for i=1, #self.怪物头像组 do
		if self.怪物头像组[i].num == 编号 then
			self.怪物头像组[i].气血 = 单位.数据.气血
			self.怪物头像组[i].最大气血 = 单位.数据.最大气血
			self.怪物头像组[i].伤害 = 单位.数据.伤害
			self.怪物头像组[i].法伤 = 单位.数据.法伤
			self.怪物头像组[i].防御 = 单位.数据.防御
			self.怪物头像组[i].法防 = 单位.数据.法防
			self.怪物头像组[i].速度 = 单位.数据.速度
			break
		end
	end
end

function 战斗类:鬼魂复活(内容)
	self.战斗单位[ljcs][内容.id].停止更新=false
	self.战斗单位[ljcs][内容.id]:换动作("待战")
	self.战斗单位[ljcs][内容.id]:设置掉血(内容.气血,2)
end
function 战斗类:取消状态(内容)
	if self.战斗单位[ljcs][内容.id] == nil then  return end
	self.战斗单位[ljcs][内容.id].状态特效[内容.名称]=nil
	if 内容.名称=="金刚护法" or 内容.名称=="分身术" or 内容.名称=="修罗隐身" or 内容.名称=="楚楚可怜" then
		self.战斗单位[ljcs][内容.id]:取消变相()
	end
end
function 战斗类:刷新技能(内容)
	self.战斗单位[内容.id].主动技能=内容.主动技能
end
function 战斗类:更新(dt,x,y)
	dt=dt
	-- local MAX_DT = 5               -- 正常情况下的最大 dt 值
	-- local ALLOWED_SPIKE = 30       -- 单次允许的最大值
	-- local MAX_EXCEPTIONS = 3       -- 连续异常计数达到 3 次触发封禁
	-- local RECOVERY_THRESHOLD = 2   -- 恢复计数达到 2 次则清零异常计数

	-- self.异常计数 = self.异常计数 or 0
	-- self.恢复计数 = self.恢复计数 or 0
	-- local dt_samples = self.dt_samples or {} -- 存储最近几帧的 dt 值
	-- self.dt_samples = dt_samples
	-- local avg_dt = 0

	-- -- 更新采样数据
	-- table.insert(dt_samples, dt)
	-- if #dt_samples > 10 then
	--     table.remove(dt_samples, 1) -- 保证采样帧数不超过限制
	-- end

	-- -- 计算平均值
	-- for _, sample in ipairs(dt_samples) do
	--     avg_dt = avg_dt + sample
	-- end
	-- avg_dt = avg_dt / #dt_samples

	-- -- 检测逻辑
	-- if dt > ALLOWED_SPIKE then
	--     -- 单次 dt 超过极限值，记录为异常
	--     self.异常计数 = self.异常计数 + 1
	--     self.恢复计数 = 0
	--     print("检测到单次极限异常 dt：" .. dt)
	-- elseif avg_dt > MAX_DT then
	--     -- 平均值异常，计为异常
	--     self.异常计数 = self.异常计数 + 1
	--     self.恢复计数 = 0
	--     print("检测到平均值异常 avg_dt：" .. avg_dt)
	-- else
	--     -- 正常情况下，尝试恢复异常计数
	--     self.恢复计数 = self.恢复计数 + 1
	--     if self.恢复计数 >= RECOVERY_THRESHOLD then
	--         self.异常计数 = 0 -- 清零异常计数
	--     end
	-- end

	-- -- 如果异常计数达到阈值，则触发封禁逻辑
	-- if self.异常计数 >= MAX_EXCEPTIONS then
	--     写配置("./config.ini", "mhxy", "封禁原因6", "跳过封号6：" .. dt)
	--     发送数据(5825.1, {"战斗加速2：" .. dt}) -- 保留原逻辑
	-- else
	--     -- 保留发送数据，但添加日志内容供分析
	--     发送数据(5825.1, {
	--         "战斗加速2：" .. dt,
	--         "当前异常计数：" .. self.异常计数,
	--         "平均值 avg_dt：" .. avg_dt,
	--         "恢复计数：" .. self.恢复计数
	--     })
	-- end


	for n=1,#self.战斗单位[ljcs] do
		if self.进程=="执行" then
			self.战斗单位[ljcs][n]:更新(dt,x,y)
		else
			if self.战斗单位[ljcs][n].敌我==1 and self.战斗单位[ljcs][n].动作=="待战" then
				self.战斗单位[ljcs][n]:更新(dt*0.3,x,y)
			else
				self.战斗单位[ljcs][n]:更新(dt,x,y)
			end
		end
		if self.战斗单位[ljcs][n]:是否选中(x,y) and 引擎.鼠标弹起(右键)  then
			self.战斗单位[ljcs][n]:设置鼠标跟随()
		end
	end
	if 引擎.鼠标弹起(0x2) and 1==2 then
		local 敌方位置={}
		local 我方位置={}
		for n=1,#self.战斗单位[ljcs] do
			if self.战斗单位[ljcs][n].敌我==1 then
				我方位置[self.战斗单位[ljcs][n].数据.位置]={x=self.战斗单位[ljcs][n].显示xy.x,y=self.战斗单位[ljcs][n].显示xy.y}
			else
				敌方位置[self.战斗单位[ljcs][n].数据.位置]={x=self.战斗单位[ljcs][n].显示xy.x,y=self.战斗单位[ljcs][n].显示xy.y}
			end
		end
	end
	if self.进程=="命令" then
		self.战斗指令:更新(dt,x,y)
	end
	if self.进程=="计算" and self.战斗流程~=nil and self.战斗流程[1]~=nil then
		if self.战斗流程[1].允许==false then
			if self.战斗流程[1].提示==nil or self.战斗流程[1].提示.允许==false  then
				self.战斗流程[1].可以执行=true
			elseif self.战斗流程[1].提示.类型=="法术" and self.战斗流程[1].可以执行==nil then
				if self.战斗流程[1].特技名称==nil  then
					if self.战斗流程[1].提示.允许 then
						self:添加战斗提醒文字(self.战斗流程[1].提示.名称)
						local lsmc = 分割文本(self.战斗流程[1].提示.名称,"使用了")
						local 临时音乐=引擎.取音效(lsmc[2])
						if 临时音乐~=nil and 临时音乐.文件~=nil then
						 	tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
						end
					end
				elseif self.战斗流程[1].提示.允许 then
					self.Tejitime=50
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:添加特技内容(self.战斗流程[1].特技名称)
				end
				self.战斗流程[1].可以执行=true
			end
		end
		if self.战斗流程[1].战斗提示 and self.战斗流程[1].战斗提示.内容 and self.战斗流程[1].战斗提示.编号 then
			self.战斗单位[ljcs][self.战斗流程[1].战斗提示.编号]:设置提示(self.战斗流程[1].战斗提示.内容)
			self.战斗流程[1].战斗提示 = nil
		end
		if self.Tejitime then
			self.Tejitime=self.Tejitime-1
			if self.Tejitime<0 then
				self.Tejitime=nil
				local 临时音乐=引擎.取音效(self.战斗流程[1].特技名称)
			    if tp.游戏音效>0 and 临时音乐~=nil then
			      tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
			    end
			end
		end
		if self.战斗流程[1].可以执行 and self.Tejitime==nil then
			self.战斗流程[1].允许=true
			self.执行流程=self.战斗流程[1].流程
			self.进程="执行"
		end
		if self.背景状态 ~= nil then
			self.背景状态 = 1
		end
	elseif self.进程=="执行" then
		self:流程更新()
		if self.拼接特效 ~= nil then
			for n=1,#self.拼接特效 do
				if self.拼接特效[n]~=nil then
					if self.拼接特效[n].延时~= nil then
						self.拼接特效[n].延时 = self.拼接特效[n].延时 -1
						if self.拼接特效[n].延时== 1 then
							self.拼接特效[n].延时 = nil
						end
					else
						self.拼接特效[n].特效:更新(dt*self.全屏加速)
						if self.掉血帧~=nil then
							if n==self.掉血特效 then
								if self.拼接特效[n].特效.当前帧 >= self.掉血帧 then
									self.掉血流程=nil
								end
							end
							if self.拼接特效[n].特效.当前帧>=self.拼接特效[n].特效.结束帧 then
								self.拼接特效[n] = nil
								self.掉血帧 = nil
								table.remove(self.拼接特效,n)
							end
						else
							if self.拼接特效[n].特效.当前帧>=self.拼接特效[n].特效.结束帧 then
								self.拼接特效[n] = nil
								table.remove(self.拼接特效,n)
							end
							if #self.拼接特效==n and self.拼接特效[n].特效.当前帧==math.floor(self.拼接特效[n].特效.结束帧/1.5) then self.掉血流程=nil end
						end
					end
				end
			end
			if #self.拼接特效==0 then
				self.拼接特效=nil
				self.背景状态=nil
			end
		end
	end
end

function 战斗类:施法流程(原始流程,攻击方,挨打方,特效,全屏)
	self:清理特效状态()
	self.施法信息={原始流程=原始流程,攻击方=攻击方,挨打方=挨打方,特效=特效,全屏=全屏}
	self.掉血流程=0
	self.执行流程="施法流程"
	self.jsq=os.time()
end
function 战斗类:清理特效状态()
    -- 清理特效相关状态
    if self.拼接特效 and type(self.拼接特效) == "table" then
        for i=1, #self.拼接特效 do
            if self.拼接特效[i] and self.拼接特效[i].特效 and type(self.拼接特效[i].特效) == "table" and self.拼接特效[i].特效.释放 then
                self.拼接特效[i].特效:释放()
            end
        end
    end
    self.拼接特效 = nil
    self.背景状态 = nil
    self.当前特效 = nil

    -- 清理施法信息
    if self.施法信息 then
        self.施法信息 = nil
    end

    -- 重置其他相关状态
    self.掉血流程 = nil
    --collectgarbage("collect")
end
function 战斗类:施法流程更新()
	if self.施法信息 and os.time()-self.jsq>=1 then
		if type(self.施法信息.挨打方) == "table" then
			if self.施法信息.全屏 then
				self:置全屏技能(self.施法信息.特效,self.战斗单位[ljcs][self.施法信息.挨打方[1].挨打方])
			else
				for i=1,#self.施法信息.挨打方 do
					if self.施法信息.特效 == "飞镖" then
						self.战斗单位[ljcs][self.施法信息.挨打方[i].挨打方]:设置飞镖(self.战斗单位[ljcs][self.施法信息.攻击方].显示xy,self.战斗单位[ljcs][self.施法信息.攻击方].初始方向,self.施法信息.挨打方[i].伤害,self.施法信息.挨打方[i].死亡,"飞镖")
					else
						self.战斗单位[ljcs][self.施法信息.挨打方[i].挨打方]:添加法术特效(self.施法信息.特效)
					end
				end
			end
		else
			if self.施法信息.全屏 then
				self:置全屏技能(self.施法信息.特效,self.战斗单位[ljcs][self.施法信息.挨打方])
			else
				self.战斗单位[ljcs][self.施法信息.挨打方]:添加法术特效(self.施法信息.特效)
			end
		end
		self.执行流程 = self.施法信息.原始流程
		self.施法信息 = nil
	end
end
function 战斗类:流程更新()
	if self.战斗流程[1]==nil then
	end
	if self.执行流程==1 and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态() and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态()  then
		if self.战斗流程[1].战斗法术 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
			for i=1,#self.战斗流程[1].寄存目标 do
				self.战斗单位[ljcs][self.战斗流程[1].寄存目标[i]]:添加法术特效(self.战斗流程[1].战斗法术)
			end
			self.战斗流程[1].延时等待=os.time()+1
			self.执行流程=1.2
		elseif self.战斗流程[1].前摇掉血 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:施法前掉血(self.战斗流程[1].前摇掉血)
			self.战斗流程[1].延时等待=os.time()
			self.执行流程=1.2
		elseif self.战斗单位[ljcs][self.战斗流程[1].攻击方].模型=="上古雷神" then
		    self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动开关=false
		    self.执行流程=2
		elseif self.战斗流程[1].全屏法术 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
			self:施法流程(1.1,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].全屏法术,true)
		elseif self.战斗流程[1].全屏22 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
			self:施法流程(1.3,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].全屏22,true)
		elseif self.战斗流程[1].挨打方[1].幻境术 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].幻境术].显示xy.x,self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].幻境术].显示xy.y
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].幻境术]:添加特技内容("反弹")
		else
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动开关=true
			if self.战斗流程[1].友伤 == true then
				self.战斗单位[ljcs][self.战斗流程[1].攻击方].友伤 = true
				self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取移动坐标("友伤")
			else
				self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取移动坐标("挨打")
			end
			self.执行流程=2
		end
	elseif self.执行流程==1.2 and os.time()-self.战斗流程[1].延时等待>=1 then
		self.战斗流程[1].延时等待=nil
		self.执行流程=1.1
	elseif self.执行流程==1.3  then
		for i=1,#self.战斗流程[1].寄存目标 do
			self.战斗单位[ljcs][self.战斗流程[1].寄存目标[i]]:换动作("挨打",nil,true)
			self.战斗单位[ljcs][self.战斗流程[1].寄存目标[i]]:开启击退(nil,self.战斗单位[ljcs][self.战斗流程[1].寄存目标[i]]:取移动坐标("返回"))
		end
		self.战斗流程[1].延时等待=os.time()
		self.执行流程=1.4
	elseif self.执行流程==1.4 and os.time()-self.战斗流程[1].延时等待>=2 then
		self.战斗流程[1].延时等待=nil
		self.执行流程=1.1
	elseif self.执行流程==1.1 and self.掉血流程==nil and self.拼接特效==nil then
		self.战斗流程[1].延时1=nil
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动开关=true

		self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取移动坐标("挨打")
		self.执行流程=2
	elseif self.执行流程==2 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动开关==false then
		if self.战斗流程[1].保护数据==nil then
			-- 添加暂停
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:暂停(0.2)
			self.执行流程=2.5
		else
			self.执行流程=10
		end
	elseif self.执行流程==2.5 then
		if self.战斗单位[ljcs][self.战斗流程[1].攻击方]:更新暂停() then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("攻击",true,nil,self.战斗流程[1].结尾气血,self.战斗流程[1].结尾死亡)
			if not self.战斗流程[1].躲避 then
			    if self.战斗单位[ljcs][self.战斗流程[1].攻击方].攻击抖动 then
			        self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].物理抖动开关 = true
			    end
			    if self.战斗单位[ljcs][self.战斗流程[1].攻击方].攻击抖动2 then
			        self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].物理抖动开关2 = true
			    end
			end
			self.执行流程=3
		end
	elseif self.执行流程==3 then
		if self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取中间()+self.战斗单位[ljcs][self.战斗流程[1].攻击方].攻击帧  then
			if self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画.攻击方式==1 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:设置弓弩(self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy)
				self.执行流程=3.2
			else
				self.执行流程=3.1
			end
		end
	elseif self.执行流程==3.1 then
		if self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取中间()+self.战斗单位[ljcs][self.战斗流程[1].攻击方].攻击帧 then
			if self.战斗流程[1].躲避 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:开启躲避()
				self.执行流程=7
			elseif self.战斗流程[1].假动作 then
				self.执行流程=7
			elseif self.战斗流程[1].共生 then
				for i=1,#self.战斗流程[1].共生 do
					if self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方] ~= nil then
						self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:设置掉血(self.战斗流程[1].共生[i].伤害,1)
						self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:换动作("挨打",nil,true)
						self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:开启击退(self.战斗流程[1].共生[i].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
					end
				end
				if self.战斗流程[1].挨打方[1].死亡~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画:置帧率("攻击",0.19)
				end
				for n=1,#self.战斗流程[1].挨打方[1].特效 do
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[n])
				end
				self.执行流程=17
			else
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:换动作(self.战斗流程[1].挨打方[1].动作,nil,true)
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:抖动挨打音效()
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,self.战斗流程[1].挨打方[1].伤害类型)
				if self.战斗流程[1].挨打方[1].死亡~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画:置帧率("攻击",0.19)
				end
				for n=1,#self.战斗流程[1].挨打方[1].特效 do
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[n])
				end
				if self.战斗流程[1].吸血伤害~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].吸血伤害,2)
				end
				if self.战斗流程[1].反震伤害~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡,self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取移动坐标("返回"))
					self.执行流程=8
				else
					self.执行流程=17
				end
			end
		end
	elseif self.执行流程==3.2 then
		if not self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].弓弩开关 then
			if self.战斗流程[1].躲避 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:开启躲避()
				self.执行流程=7
			elseif self.战斗流程[1].假动作 then
				self.执行流程=7
			elseif self.战斗流程[1].共生 then
				for i=1,#self.战斗流程[1].共生 do
					if self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方] ~= nil then
						self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:设置掉血(self.战斗流程[1].共生[i].伤害,1)
						self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:换动作("挨打",nil,true)
						self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:开启击退(self.战斗流程[1].共生[i].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
					end
				end
				if self.战斗流程[1].挨打方[1].死亡~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画:置帧率("攻击",0.19)
				end
				for n=1,#self.战斗流程[1].挨打方[1].特效 do
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[n])
				end
				self.执行流程=17
			else
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:换动作(self.战斗流程[1].挨打方[1].动作,nil,true)
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,self.战斗流程[1].挨打方[1].伤害类型)
				if self.战斗流程[1].挨打方[1].死亡~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画:置帧率("攻击",0.19)
				end
				for n=1,#self.战斗流程[1].挨打方[1].特效 do
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[n])
				end
				if self.战斗流程[1].吸血伤害~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].吸血伤害,2)
				end
				if self.战斗流程[1].反震伤害~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡,self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取移动坐标("返回"))
					self.执行流程=8
				else
					self.执行流程=17
				end
			end
		end
	elseif self.执行流程==4 and ((self.战斗流程[1].挨打方[1]==nil or self.战斗流程[1].挨打方[1].挨打方==nil or self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]==nil) or self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态()) then
		if self.战斗流程[1].反击伤害~=nil and  (self.战斗流程[1].挨打方[1]~=nil and self.战斗流程[1].挨打方[1].挨打方~=nil and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]~=nil) then
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:换动作("攻击",true)
			if self.战斗流程[1].反击死亡~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].动画:置帧率("攻击",0.11)
			end
			self.执行流程=16
		elseif self.战斗流程[1].返回 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].返回开关=true
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回")
			self.执行流程=5
		else
			self.执行流程=6
		end
	elseif self.执行流程==5 and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态() then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换方向(self.战斗单位[ljcs][self.战斗流程[1].攻击方].初始方向)
		self.执行流程=6
	elseif self.执行流程== "施法流程" then
		self:施法流程更新()
	elseif self.执行流程==6 then
		for n=1,#self.战斗单位[ljcs] do
			if self.战斗单位[ljcs][n]:取状态()==false then return  end
		end
		self:流程更新二段(true)
	elseif self.执行流程==7 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战"  then
		self.执行流程=4
	elseif self.执行流程==8 and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态() and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态() then
		if self.战斗流程[1].反震死亡==nil  then
			self.执行流程=4
		else
			self.执行流程=6
		end
	elseif self.执行流程==9 and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态() and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态() then
		if self.战斗流程[1].反击死亡==nil then
			self.战斗流程[1].反击伤害=nil
			self.执行流程=4
		else
			self.执行流程=6
		end
	elseif self.执行流程==10 then
		self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号].保护 = true
		self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号].移动开关=true
		self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].显示xy.x,self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].显示xy.y
		self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号]:添加攻击特效("保护")
		local 临时音乐=引擎.取音效("保护")
	    if tp.游戏音效>0 and 临时音乐~=nil then
	      tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
	    end
		self.执行流程=11
	elseif self.执行流程==11 and self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号].移动开关==false then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("攻击",true,nil,self.战斗流程[1].添加状态,self.战斗流程[1].取消状态,self.战斗流程[1].结尾气血)
		if self.战斗流程[1].挨打方[1].死亡~=nil then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画:置帧率("攻击",0.11)
		end
		self.执行流程=12
	elseif self.执行流程==12 then
		if  self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取中间()+self.战斗单位[ljcs][self.战斗流程[1].攻击方].攻击帧  then
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:换动作(self.战斗流程[1].挨打方[1].动作,nil,true,self.战斗流程[1].挨打方[1].添加状态,self.战斗流程[1].挨打方[1].取消状态)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,self.战斗流程[1].挨打方[1].伤害类型)
			self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号]:换动作("挨打",nil,true)
			self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号]:开启击退(self.战斗流程[1].保护数据.死亡)
			self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号]:设置掉血(self.战斗流程[1].保护数据.伤害,self.战斗流程[1].挨打方[1].伤害类型)
			for n=1,#self.战斗流程[1].挨打方[1].特效 do
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[n])
			end
			if self.战斗流程[1].吸血伤害~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].吸血伤害,2)
			end
			self.执行流程=13
		end
	elseif self.执行流程==13 and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态() and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态() and self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号]:取状态() then
		if self.战斗流程[1].保护数据.死亡~=nil then
			self.执行流程=4
		else
			self.执行流程=14
		end
	elseif self.执行流程==14 then
		self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号].返回开关=true
		self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].保护数据.编号]:取移动坐标("返回")
		self.执行流程=15
	elseif self.执行流程==15 then
		self.执行流程=4
	elseif self.执行流程==16 then
		if  self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取间隔() >= self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取中间()+self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].攻击帧  then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反击伤害,1)
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反击死亡,self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取移动坐标("返回"))
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:添加攻击特效("暴击")
			self.执行流程=9
		end
	elseif self.执行流程==17 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
		if self.战斗流程[1].反击伤害~=nil then
			self.执行流程=4
		elseif self.战斗流程[1].返回 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].返回开关=true
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回")
			self.执行流程=5
		else
			self.执行流程=6
		end
	elseif self.执行流程==20 then
	    if not self.战斗流程[1].延时等待 then
	        self.战斗流程[1].延时等待 = os.clock()
	    end

	    if os.clock() - self.战斗流程[1].延时等待 >= 1.5 then
	        self.战斗流程[1].延时等待 = nil
	        self.战斗单位[ljcs][self.战斗流程[1].攻击方].是否显示=false
	        if self.战斗流程[1].追加~=nil then
	            self.战斗单位[ljcs][self.战斗流程[1].追加].是否显示=false
	        end
	        self.执行流程=21
	    end
	elseif self.执行流程==21   then
		if self.战斗流程[1].结束 and 引擎.场景.队伍[1].数字id==self.战斗流程[1].id then
			发送数据(5506.1)
			self.执行流程=999999
		else
			self.执行流程=6
		end
	elseif self.执行流程==50 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true,nil,self.战斗流程[1].结尾气血,self.战斗流程[1].结尾死亡)
		if self.战斗流程[1].群封 == false then
			self:施法流程(51,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
		else
			if self.战斗流程[1].全屏 then
				self:施法流程(51.1,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],true)
			else
				self:施法流程(51.1,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
			end
		end
	elseif self.执行流程==51 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
		if self.战斗流程[1].反弹 then
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:不掉血文字("反弹")
		end
		self.执行流程=52
	elseif self.执行流程==51.1 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0  then
		for n=1,#self.战斗流程[1].挨打方 do
			if self.战斗流程[1].挨打方[n].反弹 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:不掉血文字("反弹")
			end
		end
		self.执行流程=52
	elseif self.执行流程==52 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
		self.执行流程=6
	elseif self.执行流程==53 and self.拼接特效==nil then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true,nil,self.战斗流程[1].结尾气血,self.战斗流程[1].结尾死亡)
		if self.战斗流程[1].全屏 == true then
			self:施法流程(54.1,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],true)
		else
			self:施法流程(54,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
		end
	elseif self.执行流程==54.1 and self.战斗流程[1].全屏 == true and self.拼接特效==nil then
		self.执行流程=54
	elseif self.执行流程==54 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
		for n=1,#self.战斗流程[1].挨打方 do
			if self.战斗流程[1].挨打方[n].恢复气血~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,2)
			end
		end
		self.执行流程=55
	elseif self.执行流程==55 and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].掉血开关==false and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战"  then
		self.执行流程=6
	elseif self.执行流程==56 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:施法流程(57,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
	elseif self.执行流程==57 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
		if self.战斗流程[1].挨打方[1].气血~=nil then
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].气血,1)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:换动作("挨打",nil,true)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
		elseif self.战斗流程[1].挨打方[1].击退~=nil then
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:换动作("挨打",nil,true)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:开启击退(nil)
		end
		self.执行流程=58
	elseif self.执行流程==58 and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态() then
		if self.战斗流程[1].挨打方[1].特效[1] == "笑里藏刀" and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].单位id == 引擎.场景.队伍[1].数字id then
			引擎.场景.队伍[1].愤怒=self.战斗流程[1].愤怒
		end
		if self.战斗流程[1].增加气血~=nil then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].增加气血,2)
		end
		self.执行流程=59
	elseif self.执行流程==59 and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态() then
		self.执行流程=6
	elseif self.执行流程==60 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		if self.战斗流程[1].扣除气血~=nil then
			if self.战斗流程[1].全屏法术 then
				self:施法流程(63,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],true)
			else
				self:施法流程(63,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
			end
		else
			if self.战斗流程[1].全屏法术 then
				self:施法流程(61,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],true)
			else
				self:施法流程(61,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
			end
		end
	elseif self.执行流程==61 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0  and self.掉血流程==nil then
		if self.战斗流程[1].愤怒 then
			引擎.场景.队伍[1].愤怒=self.战斗流程[1].愤怒
		end
		for n=1,#self.战斗流程[1].挨打方 do
			if self.战斗流程[1].挨打方[n].恢复气血~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,2)
			end
			if self.战斗流程[1].挨打方[n].恢复伤势~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:同步伤势(nil,self.战斗流程[1].挨打方[n].恢复伤势)
			end
			if self.战斗流程[1].挨打方[n].复活~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:换动作("待战")
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].停止更新=false
			end
		end
		self.执行流程=62
	elseif self.执行流程==62 and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态() and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战"  then
		local 条件通过=#self.战斗流程[1].挨打方
		for n=1,#self.战斗流程[1].挨打方 do
			if self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取状态() then
				条件通过=条件通过-1
			end
		end
		if 条件通过<=0 then
			self.执行流程=6
		end
	elseif self.执行流程==63 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" and self.掉血流程==nil then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].扣除气血,1)
		self.执行流程=64
	elseif self.执行流程==64 and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态() then
		self.执行流程=61
	elseif self.执行流程==108 then
		for n=#self.战斗流程[1].挨打方, 1, -1 do
			if self.战斗流程[1].挨打方[n].挨打方==nil or self.战斗流程[1].挨打方[n].挨打方==0 or self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]==nil then
				table.remove(self.战斗流程[1].挨打方, n)
			end
		end
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true,nil,self.战斗流程[1].结尾气血,self.战斗流程[1].结尾死亡)
		self:施法流程(109,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
	elseif self.执行流程==109 and (#self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 or self.掉血流程==nil) then
		for n=1,#self.战斗流程[1].挨打方 do
			if self.战斗流程[1].挨打方[n].特效[2]~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
			end
			if self.战斗流程[1].挨打方[n].躲避 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("躲避")
			elseif self.战斗流程[1].挨打方[n].免疫 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("免疫")
			elseif self.战斗流程[1].挨打方[n].无穷 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("无穷")
			elseif self.战斗流程[1].挨打方[n].反弹 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("反弹")
			else
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型)
			end
			if self.战斗流程[1].挨打方[n].死亡 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打",nil,true)
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
			else
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启转圈()
			end
			if self.战斗流程[1].反震伤害~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
				self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
				self.战斗单位[ljcs][self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡,self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取移动坐标("返回"))
			end
		end
		self.执行流程=110
	elseif self.执行流程==110 then
		self.战斗流程[1].等待计时=os.time()
		self.执行流程=111
	elseif self.执行流程==111 then
		if os.time()-self.战斗流程[1].等待计时>=1 then
			for n=1,#self.战斗流程[1].挨打方 do
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:关闭转圈()
				--self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].动画:置方向(self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].转圈方向,"待战")
			end
			self.战斗流程[1].等待计时=nil
			self.执行流程=202
		end
	elseif self.执行流程==200 then
		for n=#self.战斗流程[1].挨打方,1,-1 do
			if self.战斗流程[1].挨打方[n].挨打方==nil or self.战斗流程[1].挨打方[n].挨打方==0 or self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]==nil then
				table.remove(self.战斗流程[1].挨打方, n)
			end
		end
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true,nil,self.战斗流程[1].结尾气血,self.战斗流程[1].结尾死亡)
		if skill无需抖动[self.战斗流程[1].挨打方[1].特效[1]] then
			self:施法流程(201,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
		else
			self:施法流程(201.1,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
		end
	elseif self.执行流程==201.1 then
		for n=1,#self.战斗流程[1].挨打方 do
			-- 在设置新的法术抖动之前，先清理旧的抖动状态
			if self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方] then
				--print("【抖动调试】战斗流程201.1 - 准备设置单位"..self.战斗流程[1].挨打方[n].挨打方.."的法术抖动，技能："..tostring(self.战斗流程[1].挨打方[1].特效[1]))
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].抖动延时 = nil
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].抖动持续时间 = nil
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].最大抖动时间 = nil
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].法术抖动计时 = 0
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].法术抖动坐标 = { x = 0, y = 0 }
			end
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].法术抖动开关 = self.战斗流程[1].挨打方[1].特效[1]
		end
		self.执行流程=203
	elseif self.执行流程==203 and (#self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 or self.掉血流程==nil) then
		if self.战斗流程[1].共生 then
			for n=1,#self.战斗流程[1].挨打方 do
				if self.战斗流程[1].挨打方[n].特效[2]~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
				end
			end
			for i=1,#self.战斗流程[1].共生 do
				if self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方] ~= nil then
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:设置掉血(self.战斗流程[1].共生[i].伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:开启击退(self.战斗流程[1].共生[i].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				end
			end
		else
			for n=1,#self.战斗流程[1].挨打方 do
				if self.战斗流程[1].挨打方[n].特效[2]~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
				end
				if self.战斗流程[1].挨打方[n].躲避 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("躲避")
				elseif self.战斗流程[1].挨打方[n].免疫 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("免疫")
				elseif self.战斗流程[1].挨打方[n].无穷 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("无穷")
				elseif self.战斗流程[1].挨打方[n].反弹 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("反弹")
				else
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型)
				end
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:抖动挨打音效()
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				if self.战斗流程[1].反震伤害~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡,self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取移动坐标("返回"))
				end
			end
		end
		self.执行流程=202
	elseif self.执行流程==201 and (#self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 or self.掉血流程==nil) then
		if self.战斗流程[1].共生 then
			for n=1,#self.战斗流程[1].挨打方 do
				if self.战斗流程[1].挨打方[n].特效[2]~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
				end
			end
			for i=1,#self.战斗流程[1].共生 do
				if self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方] ~= nil then
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:设置掉血(self.战斗流程[1].共生[i].伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:开启击退(self.战斗流程[1].共生[i].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				end
			end
		else
			for n=1,#self.战斗流程[1].挨打方 do
				if self.战斗流程[1].挨打方[n].特效[2]~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
				end
				if self.战斗流程[1].挨打方[n].躲避 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("躲避")
				elseif self.战斗流程[1].挨打方[n].免疫 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("免疫")
				elseif self.战斗流程[1].挨打方[n].无穷 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("无穷")
				elseif self.战斗流程[1].挨打方[n].反弹 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("反弹")
				else
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型)
				end
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打",nil,true)
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				if self.战斗流程[1].反震伤害~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡,self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取移动坐标("返回"))
				end
			end
		end
		self.执行流程=202
	elseif self.执行流程==202 then
		local 条件通过=#self.战斗流程[1].挨打方
		for n=1,#self.战斗流程[1].挨打方 do
			if self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取状态() then
				条件通过=条件通过-1
			else
				条件通过=条件通过-1
			end
		end
		if 条件通过<=0 and self.拼接特效==nil then
			self.执行流程=6
		end
	elseif self.执行流程==205 and self.拼接特效==nil then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		if skill无需抖动[self.战斗流程[1].挨打方[1].特效[1]] then
			self:施法流程(206,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],true)
		else
			self:施法流程(206.1,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],true)
		end
	elseif self.执行流程==206 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 and self.掉血流程==nil then
		self.执行流程=201
	--法术抖动
	elseif self.执行流程==206.1 then
		for n=1,#self.战斗流程[1].挨打方 do
			-- 在设置新的法术抖动之前，先清理旧的抖动状态
			if self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方] then
				--print("【抖动调试】战斗流程206.1 - 准备设置单位"..self.战斗流程[1].挨打方[n].挨打方.."的法术抖动，技能："..tostring(self.战斗流程[1].挨打方[1].特效[1]))
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].抖动延时 = nil
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].抖动持续时间 = nil
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].最大抖动时间 = nil
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].法术抖动计时 = 0
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].法术抖动坐标 = { x = 0, y = 0 }
			end
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方].法术抖动开关 = self.战斗流程[1].挨打方[1].特效[1]
		end
		self.执行流程=207
	elseif self.执行流程==207 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 and self.掉血流程==nil then
		if self.战斗流程[1].共生 then
			for n=1,#self.战斗流程[1].挨打方 do
				if self.战斗流程[1].挨打方[n].特效[2]~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
				end
			end
			for i=1,#self.战斗流程[1].共生 do
				if self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方] ~= nil then
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:设置掉血(self.战斗流程[1].共生[i].伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:开启击退(self.战斗流程[1].共生[i].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				end
			end
		else
			for n=1,#self.战斗流程[1].挨打方 do
				if self.战斗流程[1].挨打方[n].特效[2]~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
				end
				if self.战斗流程[1].挨打方[n].躲避 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("躲避")
				elseif self.战斗流程[1].挨打方[n].免疫 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("免疫")
				elseif self.战斗流程[1].挨打方[n].无穷 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("无穷")
				elseif self.战斗流程[1].挨打方[n].反弹 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("反弹")
				else
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型)
				end
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:抖动挨打音效()
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				if self.战斗流程[1].反震伤害~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡,self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取移动坐标("返回"))
				end
			end
		end
		self.执行流程=202

	elseif self.执行流程==208 then --只施法
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:施法流程(209,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
	elseif self.执行流程==209 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 and self.掉血流程==nil then
		self.执行流程=6
	elseif self.执行流程==210 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("攻击3",true)
		self.执行流程=211
	elseif self.执行流程==211 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
		self.执行流程=6
	elseif self.执行流程==300 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置抓捕动画(self.战斗流程[1].挨打方[1].挨打方,self.战斗流程[1].宝宝,self.战斗流程[1].捕捉成功,self.战斗流程[1].名称)
		self.执行流程=301
	elseif self.执行流程==301 and  self.战斗单位[ljcs][self.战斗流程[1].攻击方].抓捕开关==false then
		self.执行流程=6
	elseif self.执行流程==311 then
		self:施法流程(201.2,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
	elseif self.执行流程==201.2 then
		if self.战斗流程[1].共生 then
			for n=1,#self.战斗流程[1].挨打方 do
				if self.战斗流程[1].挨打方[n].特效[2]~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
				end
			end
			for i=1,#self.战斗流程[1].共生 do
				if self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方] ~= nil then
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:设置掉血(self.战斗流程[1].共生[i].伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].共生[i].挨打方]:开启击退(self.战斗流程[1].共生[i].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				end
			end
		else
			for n=1,#self.战斗流程[1].挨打方 do
				if self.战斗流程[1].挨打方[n].特效[2]~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
				end
				if self.战斗流程[1].挨打方[n].躲避 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("躲避")
				elseif self.战斗流程[1].挨打方[n].免疫 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("免疫")
				elseif self.战斗流程[1].挨打方[n].无穷 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("无穷")
				elseif self.战斗流程[1].挨打方[n].反弹 then
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:不掉血文字("反弹")
				else
					self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型)
				end
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打",nil,true)
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
				if self.战斗流程[1].反震伤害~=nil then
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
					self.战斗单位[ljcs][self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡,self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取移动坐标("返回"))
				end
			end
		end
		self.执行流程=6
	elseif self.执行流程==500 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].伤害,1)
		self.执行流程=501
	elseif self.执行流程==501 and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态() then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:施法流程(502,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
	elseif self.执行流程==502 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
		for n=1,#self.战斗流程[1].挨打方 do
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打",nil,true)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
		end
		self.执行流程=503
	elseif self.执行流程==503 then
		local 条件通过=#self.战斗流程[1].挨打方
		for n=1,#self.战斗流程[1].挨打方 do
			if self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取状态() then
				条件通过=条件通过-1
			end
		end
		if 条件通过<=0 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].是否显示=false
			self.执行流程=6
		end
	elseif self.执行流程==900 then
		if self.战斗流程[1].id==引擎.场景.队伍[1].数字id or self.战斗流程[1].id==0 then
			引擎.场景.常规提示:打开(self.战斗流程[1].内容)
		end
		self.执行流程=6
	elseif self.执行流程==901 then
		if self.战斗流程[1].id==引擎.场景.队伍[1].数字id or self.战斗流程[1].id==0 then
			引擎.场景.窗口.消息框:添加文本(self.战斗流程[1].内容)
		end
		self.执行流程=6
	elseif self.执行流程==600 then
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]=战斗单位类()
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:创建单位(self.战斗流程[1].挨打方[1].数据,self.战斗流程[1].挨打方[1].队伍,self.战斗流程[1].挨打方[1].挨打方)
		self.执行流程=6
	elseif self.执行流程==600.1 then
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]=战斗单位类()
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:创建单位(self.战斗流程[1].挨打方[1].数据,self.战斗流程[1].挨打方[1].队伍,self.战斗流程[1].挨打方[1].挨打方)
		local 临时音乐=引擎.取音效("轮回召唤")
	    if tp.游戏音效>0 and 临时音乐~=nil then
	      tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
	    end
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("轮回召唤")

		self.执行流程=6
	elseif self.执行流程==601 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].方向=self.战斗单位[ljcs][self.战斗流程[1].攻击方].逃跑方向
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("返回")
		if self.战斗流程[1].喊话 ~= nil then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].喊话:写入(self.战斗单位[ljcs][self.战斗流程[1].攻击方],self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画.动画,self.战斗流程[1].喊话)
		end
		self.战斗流程[1].等待计时=os.time()
		self.执行流程=602
	elseif self.执行流程==602 and os.time()-self.战斗流程[1].等待计时>=3 then
		if self.战斗流程[1].成功==false then
			local 临时音乐=引擎.取音效("逃跑失败")
		    if tp.游戏音效>0 and 临时音乐~=nil then
		      tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
		    end
			self.执行流程=603
		else
			local 临时音乐=引擎.取音效("逃跑成功")
			if  临时音乐~=nil and 临时音乐.文件~=nil then
				tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
			end
			self.执行流程=605
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].逃跑开关=true
			if self.战斗流程[1].追加~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].追加].方向=self.战斗单位[ljcs][self.战斗流程[1].追加].逃跑方向
				self.战斗单位[ljcs][self.战斗流程[1].追加]:换动作("返回")
				self.战斗单位[ljcs][self.战斗流程[1].追加].逃跑开关=true
			end
		end
	elseif self.执行流程==603 then
		self.战斗流程[1].等待计时=os.time()
		self.战斗流程[1].初始方向=self.战斗单位[ljcs][self.战斗流程[1].攻击方].逃跑方向
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("待战")
		self.执行流程=604
	elseif self.执行流程==604 then
		self.战斗流程[1].初始方向=self.战斗流程[1].初始方向+1
		if self.战斗流程[1].初始方向>=4 then self.战斗流程[1].初始方向=0 end
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].方向=self.战斗流程[1].初始方向
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("待战")
		if os.time()-self.战斗流程[1].等待计时>=1 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].方向=self.战斗单位[ljcs][self.战斗流程[1].攻击方].初始方向
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("待战")
			self.执行流程=6
		end
	elseif self.执行流程==605 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy.x=self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy.x+self.战斗单位[ljcs][self.战斗流程[1].攻击方].逃跑坐标
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy.y=self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy.y+self.战斗单位[ljcs][self.战斗流程[1].攻击方].逃跑坐标
		if self.战斗流程[1].追加==nil then
			if 取两点距离(生成XY(self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy.y),生成XY(self.战斗单位[ljcs][self.战斗流程[1].攻击方].初始xy.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].初始xy.y))>=500 then
				self.执行流程=606
			end
		else
			self.战斗单位[ljcs][self.战斗流程[1].追加].显示xy.x=self.战斗单位[ljcs][self.战斗流程[1].追加].显示xy.x+self.战斗单位[ljcs][self.战斗流程[1].追加].逃跑坐标
			self.战斗单位[ljcs][self.战斗流程[1].追加].显示xy.y=self.战斗单位[ljcs][self.战斗流程[1].追加].显示xy.y+self.战斗单位[ljcs][self.战斗流程[1].追加].逃跑坐标
			if 取两点距离(生成XY(self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy.y),生成XY(self.战斗单位[ljcs][self.战斗流程[1].攻击方].初始xy.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].初始xy.y))>=500 and 取两点距离(生成XY(self.战斗单位[ljcs][self.战斗流程[1].追加].显示xy.x,self.战斗单位[ljcs][self.战斗流程[1].追加].显示xy.y),生成XY(self.战斗单位[ljcs][self.战斗流程[1].追加].初始xy.x,self.战斗单位[ljcs][self.战斗流程[1].追加].初始xy.y))>=500 then
				self.执行流程=606
			end
		end
	elseif self.执行流程==606 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].是否显示=false
		if self.战斗流程[1].结束 and 引擎.场景.队伍[1].数字id==self.战斗流程[1].id then
			发送数据(5506)
			self.执行流程=999999
		else
			self.执行流程=6
		end
	elseif self.执行流程==607 then
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]=战斗单位类()
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:创建单位(self.战斗流程[1].挨打方[1].数据,self.战斗流程[1].挨打方[1].队伍,self.战斗流程[1].挨打方[1].挨打方)
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].是否显示=false
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:添加战斗提醒文字(self.战斗单位[ljcs][self.战斗流程[1].攻击方].名称.."使用了召唤")
		self.执行流程=608
	elseif self.执行流程==608 then
		if self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].是否显示=true
			self.执行流程=609
			self.战斗流程[1].延时等待=os.time()
		end
	elseif self.执行流程==609 and os.time()-self.战斗流程[1].延时等待>=1 then
		self.执行流程=6
	elseif self.执行流程==614 then
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]=战斗单位类()
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:创建单位(self.战斗流程[1].挨打方[1].数据,self.战斗流程[1].挨打方[1].队伍,self.战斗流程[1].挨打方[1].挨打方)
		self.执行流程=608
	elseif self.执行流程==610 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].状态特效[self.战斗流程[1].状态]=nil
		self.执行流程=6
	elseif self.执行流程==611 then
		if #self.战斗流程[1].挨打方==0 then
			self.执行流程=6
		else
			self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
			self:施法流程(612,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,"飞镖",false)
		end
	elseif self.执行流程==612 then
		local 结束=true
		for n=1,#self.战斗流程[1].挨打方 do
			if self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取状态()==false then
				结束=false
			end
		end
		if 结束 then
			self.执行流程=6
		end
	elseif self.执行流程==613 then
		if #self.战斗流程[1].挨打方==0 then
			self.执行流程=6
		else
			for n=1,#self.战斗流程[1].挨打方 do
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置溅射(self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy,self.战斗单位[ljcs][self.战斗流程[1].攻击方].初始方向,self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].死亡)
			end
			self.执行流程=612
		end
	elseif self.执行流程==615 then
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加法术特效(self.战斗流程[1].挨打方[1].特效[1])
		local 临时音乐=引擎.取音效("五雷轰顶")
	    if tp.游戏音效>0 and 临时音乐~=nil then
	      tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
	    end
		self.执行流程=616
	elseif self.执行流程==616 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,self.战斗流程[1].挨打方[1].类型)
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:换动作("挨打",nil,true)
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡)
		self.执行流程=617
	elseif self.执行流程==617 and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态() then
		self.执行流程=6
	elseif self.执行流程==620 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		for i=1,#self.战斗流程[1].挨打方 do
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[i].挨打方]=战斗单位类()
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[i].挨打方]:创建单位(self.战斗流程[1].挨打方[i].数据,self.战斗流程[1].挨打方[i].队伍,self.战斗流程[1].挨打方[i].挨打方)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[i].挨打方].是否显示=false
		end
		self.执行流程=621
	elseif self.执行流程==621 then
		if self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
			for i=1,#self.战斗流程[1].挨打方 do
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[i].挨打方].是否显示=true
			end
			self.执行流程=622
			self.战斗流程[1].延时等待=os.time()
		end
	elseif self.执行流程==622 and os.time()-self.战斗流程[1].延时等待>=1 then
		self.执行流程=6
	elseif self.执行流程==623.1 then --黄真召唤
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		local 临时音乐=引擎.取音效("召唤")
	    if tp.游戏音效>0 and 临时音乐~=nil then
	      tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
	    end
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:添加法术特效("黄真召唤")
		self.执行流程=624.1
	elseif self.执行流程==624.1 then
		if self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]=战斗单位类()
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:创建单位(self.战斗流程[1].挨打方[1].数据,self.战斗流程[1].挨打方[1].队伍,self.战斗流程[1].挨打方[1].挨打方)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("召唤")
			self.执行流程=624.2
			self.战斗流程[1].延时等待=os.time()
		end
	elseif self.执行流程==624.2 and os.time()-self.战斗流程[1].延时等待>=1.5 then
		self.执行流程=6
	elseif self.执行流程==623 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self.执行流程=624
	elseif self.执行流程==624 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]=战斗单位类()
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:创建单位(self.战斗流程[1].挨打方[1].数据,self.战斗流程[1].挨打方[1].队伍,self.战斗流程[1].挨打方[1].挨打方)
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("召唤")
		self.执行流程=6
		---地煞星魂
	elseif self.执行流程==625 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].是否显示=false
		self.执行流程=6
		--惊天动地
	elseif self.执行流程==400 and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态() and self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取状态()  then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:施法前掉血(self.战斗流程[1].结尾气血)
		self.执行流程=401
		self.战斗流程[1].延时等待=os.time()
	elseif self.执行流程==401 and os.time()-self.战斗流程[1].延时等待>=2  then
		self.战斗流程[1].延时等待=nil
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("惊天动地")
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动开关=true
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.y=440,-28
		self.执行流程=402
	elseif self.执行流程==402 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动开关==false then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].是否显示=false
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动开关=true
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取移动坐标("挨打",self.战斗流程[1].攻击方)
		self.执行流程=403
		self.战斗流程[1].延时等待=os.time()
	elseif self.执行流程==403 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动开关==false and os.time()-self.战斗流程[1].延时等待>=1 then
		self.战斗流程[1].延时等待=nil
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].是否显示=true
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("攻击",true)
		if self.战斗流程[1].挨打方[1].死亡~=nil then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画:置帧率("攻击",0.11)
		end
		self.执行流程=404
	elseif self.执行流程==404 then
		if self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取中间()+self.战斗单位[ljcs][self.战斗流程[1].攻击方].攻击帧  then
			if self.战斗单位[ljcs][self.战斗流程[1].攻击方].武器子类 ~= nil and self.战斗单位[ljcs][self.战斗流程[1].攻击方].武器子类 == 14 and not self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].弓弩开关 then
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:设置弓弩(self.战斗单位[ljcs][self.战斗流程[1].攻击方].显示xy,self.战斗单位[ljcs][self.战斗流程[1].攻击方].初始方向)
			end
			self.执行流程=405
		end
	elseif self.执行流程==405 then
		for n = #self.战斗流程[1].挨打方, 1, -1 do
		    if self.战斗流程[1].挨打方[n].挨打方 == nil or self.战斗流程[1].挨打方[n].挨打方 == 0 or self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方] == nil then
		        table.remove(self.战斗流程[1].挨打方, n)  -- 使用当前索引n进行删除
		    end
		end
		self.执行流程=406
	elseif self.执行流程==406 then
		for n=1,#self.战斗流程[1].挨打方 do
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打",nil,true)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
		end
		self.执行流程=407
	elseif self.执行流程==407 and ((self.战斗流程[1].挨打方[1]==nil or self.战斗流程[1].挨打方[1].挨打方==nil or self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]==nil) or self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态()) then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].返回开关=true
		self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.x,self.战斗单位[ljcs][self.战斗流程[1].攻击方].移动坐标.y=self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回")
		local 条件通过=#self.战斗流程[1].挨打方
		for n=1,#self.战斗流程[1].挨打方 do
			if self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:取状态() then
				条件通过=条件通过-1
			else
				条件通过=条件通过-1
			end
		end
		if 条件通过<=0 then
			self.执行流程=6
		end
	elseif self.执行流程==601.1 then--缩地尺/水遁/
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:施法流程(601.2,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].特效,false)
	elseif self.执行流程==601.2 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
		self.战斗流程[1].等待计时=os.time()
		self.执行流程=601.3
	elseif self.执行流程==601.3 and os.time()-self.战斗流程[1].等待计时>=2 then
		if self.战斗流程[1].成功 then
			self.战斗单位[ljcs][self.战斗流程[1].攻击方].是否显示=false
			if self.战斗流程[1].追加~=nil then
				self.战斗单位[ljcs][self.战斗流程[1].追加].是否显示=false
			end
		end
		self.执行流程=601.4
	elseif self.执行流程==601.4 then
		if self.战斗流程[1].结束 and 引擎.场景.队伍[1].数字id==self.战斗流程[1].id then
			发送数据(5506)
			self.执行流程=999999
		else
			self.执行流程=6
		end

		 --动物套变身
	elseif self.执行流程==707 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:添加战斗提醒文字(self.战斗单位[ljcs][self.战斗流程[1].攻击方].名称.."触发了动物套效果")
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:添加法术特效("召唤")
		self.执行流程=708
	elseif self.执行流程==708 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:更改模型(self.战斗流程[1].参数,1)
		self.执行流程=6
	elseif self.执行流程==709 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then--八戒上身
		self.战斗单位[ljcs][self.战斗流程[1].挨打方]:添加法术特效("召唤")
		self.战斗单位[ljcs][self.战斗流程[1].挨打方]:更改模型(self.战斗流程[1].参数,1)
		self.执行流程=6
	elseif self.执行流程==710 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then--破变身
		self.战斗单位[ljcs][self.战斗流程[1].挨打方]:更改模型(self.战斗流程[1].参数.模型,"角色",self.战斗流程[1].参数.染色方案,self.战斗流程[1].参数.染色组,self.战斗流程[1].参数.变异,self.战斗流程[1].参数.武器,nil,nil,nil,self.战斗流程[1].参数.炫彩,self.战斗流程[1].参数.炫彩组,nil,self.战斗流程[1].参数.名称)
		self.执行流程=6
	elseif self.执行流程==710.1 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then--星魂地煞
		self.战斗单位[ljcs][self.战斗流程[1].挨打方]:更改模型(self.战斗流程[1].参数.模型,"怪物",self.战斗流程[1].参数.染色方案,self.战斗流程[1].参数.染色组,self.战斗流程[1].参数.变异,self.战斗流程[1].参数.武器,nil,nil,nil,self.战斗流程[1].参数.炫彩,self.战斗流程[1].参数.炫彩组,nil,self.战斗流程[1].参数.名称)
		self.执行流程=6
	elseif self.执行流程==711 then
		self.战斗单位[ljcs][self.战斗流程[1].挨打方]:更改模型(self.战斗流程[1].参数.模型,self.战斗流程[1].参数.类型,self.战斗流程[1].参数.染色方案,self.战斗流程[1].参数.染色组,self.战斗流程[1].参数.变异,self.战斗流程[1].参数.武器,nil,nil,nil,self.战斗流程[1].参数.炫彩,self.战斗流程[1].参数.炫彩组,nil,self.战斗流程[1].参数.名称)
		self.执行流程=6
	elseif self.执行流程==800 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then --连破
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:添加战斗提醒文字(self.战斗单位[ljcs][self.战斗流程[1].攻击方].名称.."使用了连破")
		local 临时音乐=引擎.取音效("连破")
	    if tp.游戏音效>0 and 临时音乐~=nil then
	      tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
	    end
		self:施法流程(801,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,"水清诀",false)
	elseif self.执行流程==801 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
		self.执行流程=6
	elseif self.执行流程==802 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:添加战斗提醒文字(self.战斗单位[ljcs][self.战斗流程[1].攻击方].名称.."使用了披坚执锐")
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:添加披坚内容(self.战斗流程[1].技能)
		self:施法流程(801,self.战斗流程[1].攻击方,self.战斗流程[1].攻击方,"披坚执锐",false)

	elseif self.执行流程==903 then--护盾 风灵 -- 无需动作添加
		self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:增加状态(self.战斗流程[1].挨打方[1].增加状态)
		self.执行流程=6

	elseif self.执行流程==905 then--风卷残云
		self:添加战斗提醒文字(self.战斗单位[ljcs][self.战斗流程[1].攻击方].名称.."使用了风卷残云")
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self.执行流程=906
	elseif self.执行流程==906 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" and self.拼接特效==nil then
		self:添加战斗提醒文字(self.战斗单位[ljcs][self.战斗流程[1].攻击方].名称.."使用了落叶萧萧")
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		local 临时音乐=引擎.取音效("落叶萧萧")
	    if tp.游戏音效>0 and 临时音乐~=nil then
	      tp:战斗音效类(临时音乐.文件,临时音乐.资源,'1')
	    end
		self:施法流程(206.1,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,"落叶萧萧",true)

	elseif self.执行流程==907 then--施法挨打但不掉血  不拼接
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:施法流程(908,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
		if self.战斗流程[1].成功 then
			-- 在设置新的法术抖动之前，先清理旧的抖动状态
			if self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方] then
				--print("【抖动调试】战斗流程907 - 准备设置单位"..self.战斗流程[1].挨打方[1].挨打方.."的法术抖动，技能："..tostring(self.战斗流程[1].挨打方[1].特效[1]))
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].抖动延时 = nil
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].抖动持续时间 = nil
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].最大抖动时间 = nil
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术抖动计时 = 0
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术抖动坐标 = { x = 0, y = 0 }
			end
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术抖动开关 = self.战斗流程[1].挨打方[1].特效[1]
		end
	elseif self.执行流程==908 and (#self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 or self.掉血流程==nil) then
		if self.战斗流程[1].成功 then
			for n=1,#self.战斗流程[1].挨打方 do
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打",nil,true)
				self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
			end
		end
		self.执行流程=202
	elseif self.执行流程==909 and self.拼接特效==nil then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self:施法流程(910,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],true)
	elseif self.执行流程==910 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 and self.掉血流程==nil then
		for n=1,#self.战斗流程[1].挨打方 do
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打",nil,true)
			self.战斗单位[ljcs][self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
		end
		self.执行流程=202

	elseif self.执行流程==20 then -- 兵解符效果
        if not self.兵解计时 then
            self.兵解计时 = os.time()
            self.兵解次数 = 0
            self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画:置颜色(ARGB(255,255,0,0)) -- 第一次变红
        end

        if os.time() - self.兵解计时 >= 0.8 then -- 每0.8秒变换一次
            self.兵解次数 = self.兵解次数 + 1
            if self.兵解次数 % 2 == 0 then
                self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画:置颜色(4294967295) -- 恢复原色
            else
                self.战斗单位[ljcs][self.战斗流程[1].攻击方].动画:置颜色(ARGB(255,255,0,0)) -- 变红
            end
            self.兵解计时 = os.time()
        end

        if self.兵解次数 >= 4 then -- 完成两次闪烁(变红->恢复->变红->恢复)
            self.兵解计时 = nil
            self.兵解次数 = nil
            self.执行流程 = 6
        end
	elseif self.执行流程==911 then --琴音三叠
        self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
        self.执行流程=912
        self.战斗流程[1].延时等待=os.clock()
    elseif self.执行流程==912 and self.战斗单位[ljcs][self.战斗流程[1].攻击方].动作=="待战" then
        -- self.战斗流程[1].延时等待=nil
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:添加法术特效(self.战斗流程[1].挨打方[1].特效[1])

        self.执行流程=912.1
    elseif self.执行流程==912.1 and os.clock()-self.战斗流程[1].延时等待>=0.7  then
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:换动作("挨打",nil,true)
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,self.战斗流程[1].挨打方[1].类型)
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
        --self.战斗单位[ljcs][self.战斗流程[1].挨打方[2].挨打方]:添加法术特效(self.战斗流程[1].挨打方[2].特效[1])
        self.执行流程=913
        self.战斗流程[1].延时等待=os.clock()
    elseif self.执行流程==913  and os.clock()-self.战斗流程[1].延时等待>=0.2 then --and os.clock()-self.战斗流程[1].延时等待>=1
        --self.战斗流程[1].延时等待=nil
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[2].挨打方]:添加法术特效(self.战斗流程[1].挨打方[2].特效[1])
        self.执行流程=913.1
        self.战斗流程[1].延时等待=os.clock()
    elseif self.执行流程==913.1  and os.clock()-self.战斗流程[1].延时等待>=0.2 then
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[2].挨打方]:换动作("挨打",nil,true)
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[2].挨打方]:设置掉血(self.战斗流程[1].挨打方[2].伤害,self.战斗流程[1].挨打方[2].类型)
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[2].挨打方]:开启击退(self.战斗流程[1].挨打方[2].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
        self.执行流程=914
        self.战斗流程[1].延时等待=os.clock()
    elseif self.执行流程==914  and os.clock()-self.战斗流程[1].延时等待>=1  and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态() then
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[3].挨打方]:添加法术特效(self.战斗流程[1].挨打方[3].特效[1])
        self.执行流程=914.1
    elseif self.执行流程==914.1  and os.clock()-self.战斗流程[1].延时等待>=1.5 then
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[3].挨打方]:换动作("挨打",nil,true)
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[3].挨打方]:设置掉血(self.战斗流程[1].挨打方[3].伤害,self.战斗流程[1].挨打方[3].类型)
        self.战斗单位[ljcs][self.战斗流程[1].挨打方[3].挨打方]:开启击退(self.战斗流程[1].挨打方[3].死亡,self.战斗单位[ljcs][self.战斗流程[1].攻击方]:取移动坐标("返回"))
        self.执行流程=202
	elseif self.执行流程==915 then--移形换影
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:换动作("施法",true)
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:添加法术特效("五雷咒")
		self:施法流程(916,self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1].挨打方[1].特效[1],false)
	elseif self.执行流程==916 and #self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方].法术特效==0 and self.战斗单位[ljcs][self.战斗流程[1].挨打方[1].挨打方]:取状态() then
		self.执行流程=52
	elseif self.执行流程==917 then
		self.清静菩提开关=true
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:添加法术特效("清静菩提2")
		self.战斗单位[ljcs][self.战斗流程[1].挨打方]:添加法术特效("清静菩提2")
		self.执行流程=918
	elseif self.执行流程==918 then
		if self.清静菩提开关==nil then
			self.执行流程=6
		end
	elseif self.执行流程==919 then
		self.战斗单位[ljcs][self.战斗流程[1].挨打方]:添加法术特效("电刑")
		self.执行流程=6
	end
end
function 战斗类:加载生成怪物(sj)
	for i=1,#sj do
		self.战斗单位[ljcs][sj[i].挨打方]=战斗单位类()
		self.战斗单位[ljcs][sj[i].挨打方]:创建单位(sj[i].数据,sj[i].队伍,sj[i].挨打方)
		self.战斗单位[ljcs][sj[i].挨打方].是否显示=true
	end
end
function 战斗类:流程更新二段(是否执行)
	if not 是否执行 then
        写配置("./config.ini","mhxy","封禁原因4","跳过封号4")
        发送数据(5825.1,{"跳过流程二段"})
    end
	if self.战斗流程[1].附加施法 ~= nil and self.战斗流程[1].附加施法.完成 == nil and self.战斗流程[1].附加施法[1] then
		if self.战斗流程[1].附加施法[1].进度 == nil then
			self.战斗流程[1].附加施法[1].进度 =1
		end
		if self.战斗流程[1].附加施法[1].进度 ==1 then
			self.战斗单位[ljcs][self.战斗流程[1].附加施法[1].攻击方]:换动作("施法",true)
			self.战斗流程[1].附加施法[1].进度 =1.1
			self.erjsq=os.time()
		elseif self.战斗流程[1].附加施法[1].进度 ==1.1 and os.time()-self.erjsq>=1 then
			for n=1,#self.战斗流程[1].附加施法[1].挨打方 do
				self.战斗单位[ljcs][self.战斗流程[1].附加施法[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].附加施法[1].挨打方[n].特效[1])
			end
			self.战斗流程[1].附加施法[1].进度 =2
		elseif self.战斗流程[1].附加施法[1].进度 ==2 and #self.战斗单位[ljcs][self.战斗流程[1].附加施法[1].挨打方[1].挨打方].法术特效==0 then
			for n=1,#self.战斗流程[1].附加施法[1].挨打方 do
				if self.战斗流程[1].附加施法[1].挨打方[n].类型 == 2 then
					self.战斗单位[ljcs][self.战斗流程[1].附加施法[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].附加施法[1].挨打方[n].伤害,self.战斗流程[1].附加施法[1].挨打方[n].类型)
				elseif self.战斗流程[1].附加施法[1].挨打方[n].类型==1 or self.战斗流程[1].附加施法[1].挨打方[n].类型==3 or self.战斗流程[1].附加施法[1].挨打方[n].类型==3.5 then
					self.战斗单位[ljcs][self.战斗流程[1].附加施法[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].附加施法[1].挨打方[n].伤害,self.战斗流程[1].附加施法[1].挨打方[n].类型)
					self.战斗单位[ljcs][self.战斗流程[1].附加施法[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].附加施法[1].挨打方[n].死亡,self.战斗单位[ljcs][self.战斗流程[1].附加施法[1].攻击方]:取移动坐标("返回"))
				end
			end
			self.战斗流程[1].附加施法[1].完成 = true
		end
	else
		if self.战斗流程[1].附加施法 == nil then
			self.战斗流程[1].附加施法={}
		end
	end
	if self.战斗流程[1].附加施法[1]  then
		if self.战斗流程[1].附加施法[1].完成 then
			table.remove(self.战斗流程[1].附加施法,1)
		end
	else
		self.战斗流程[1].附加施法.完成 = true
	end
	if not self.战斗流程[1].附加施法.完成 then  return  end
	if self.战斗流程[1].溅射 ~= nil and  self.战斗流程[1].溅射.完成== nil then
		for i=1,#self.战斗流程[1].溅射 do
			if self.战斗单位[ljcs][self.战斗流程[1].溅射[i].挨打方] ~= nil then
				self.战斗单位[ljcs][self.战斗流程[1].溅射[i].挨打方]:设置掉血(self.战斗流程[1].溅射[i].伤害,1)
				self.战斗单位[ljcs][self.战斗流程[1].溅射[i].挨打方]:换动作("挨打",nil,true)
				self.战斗单位[ljcs][self.战斗流程[1].溅射[i].挨打方]:开启击退(self.战斗流程[1].溅射[i].死亡,self.战斗单位[ljcs][self.战斗流程[1].溅射[i].攻击方]:取移动坐标("返回"))
			end
		end
		self.战斗流程[1].溅射.完成 = true
	end
	if self.战斗流程[1].无动作掉血 ~= nil and  self.战斗流程[1].无动作掉血.完成== nil then
		for i=1,#self.战斗流程[1].无动作掉血 do
			if self.战斗单位[ljcs][self.战斗流程[1].无动作掉血[i].挨打方] ~= nil then
				self.战斗单位[ljcs][self.战斗流程[1].无动作掉血[i].挨打方]:设置掉血(self.战斗流程[1].无动作掉血[i].伤害,1)
				if self.战斗流程[1].无动作掉血[i].死亡 then
					self.战斗单位[ljcs][self.战斗流程[1].无动作掉血[i].挨打方]:死亡处理1(self.战斗流程[1].无动作掉血[i].死亡,self.战斗单位[ljcs][self.战斗流程[1].无动作掉血[i].攻击方]:取移动坐标("返回"))
				end
			end
		end
		self.战斗流程[1].无动作掉血.完成 = true
	end
	if self.战斗流程[1].无间地狱 then
		self.无间地狱开关=true
	end
	if self.战斗流程[1].媚眼如丝 then
		self.媚眼如丝开关=true
	end
	if self.战斗流程[1].状态处理添加 ~= nil and self.战斗流程[1].状态处理添加.完成 == nil then
		for i=1,#self.战斗流程[1].状态处理添加 do
			if self.战斗单位[ljcs][self.战斗流程[1].状态处理添加[i].编号].状态特效[self.战斗流程[1].状态处理添加[i].名称] == nil then
				self.战斗单位[ljcs][self.战斗流程[1].状态处理添加[i].编号]:增加状态(self.战斗流程[1].状态处理添加[i].名称,self.战斗流程[1].状态处理添加[i].回合,self.战斗流程[1].状态处理添加[i].层数,self.战斗流程[1].状态处理添加[i].颜色,self.战斗流程[1].状态处理添加[i].负面状态)
			else
				self.战斗单位[ljcs][self.战斗流程[1].状态处理添加[i].编号].状态特效[self.战斗流程[1].状态处理添加[i].名称].回合 = self.战斗流程[1].状态处理添加[i].回合
			end
		end
		self.战斗流程[1].状态处理添加.完成 = true
	end
	if self.战斗流程[1].状态处理取消 ~= nil and self.战斗流程[1].状态处理取消.完成 == nil  then
		for i=1,#self.战斗流程[1].状态处理取消 do
			if self.战斗单位[ljcs][self.战斗流程[1].状态处理取消[i].编号].状态特效[self.战斗流程[1].状态处理取消[i].名称] ~= nil then
				self.战斗单位[ljcs][self.战斗流程[1].状态处理取消[i].编号].状态特效[self.战斗流程[1].状态处理取消[i].名称]=nil
			end
		end
		self.战斗流程[1].状态处理取消.完成 = true
	end
	if self.战斗流程[1].回复气血 ~= nil and self.战斗流程[1].回复气血.完成 == nil then
		for i=1,#self.战斗流程[1].回复气血 do
			self.战斗单位[ljcs][self.战斗流程[1].回复气血[i].挨打方]:设置掉血(self.战斗流程[1].回复气血[i].气血,2)
		end
		self.战斗流程[1].回复气血.完成 = true
	end
	if self.战斗流程[1].同步气血 ~= nil and self.战斗流程[1].同步气血.完成 == nil then
		for i=1,#self.战斗流程[1].同步气血 do
			self.战斗单位[ljcs][self.战斗流程[1].同步气血[i].挨打方]:同步气血(self.战斗流程[1].同步气血[i].气血,self.战斗流程[1].同步气血[i].气血上限)
		end
		self.战斗流程[1].同步气血.完成 = true
	end
	if self.战斗流程[1].减少魔法 ~= nil and self.战斗流程[1].减少魔法.完成 == nil then
		for i=1,#self.战斗流程[1].减少魔法 do
			self.战斗单位[ljcs][self.战斗流程[1].减少魔法[i].挨打方]:魔法更新(self.战斗流程[1].减少魔法[i].魔法)
		end
		self.战斗流程[1].减少魔法.完成 = true
	end
	if not self.战斗流程[1].附加施法.完成 then  return  end
	for n=1,#self.战斗单位[ljcs] do
		if self.战斗单位[ljcs][n]:取状态()==false then return  end
		if #self.战斗流程==1 and self.战斗单位[ljcs][n]:取状态()==false then return  end
	end
	if self.战斗流程[1].添加护盾 ~= nil and self.战斗流程[1].添加护盾.完成 == nil then
		for i=1,#self.战斗流程[1].添加护盾 do
			self.战斗单位[ljcs][self.战斗流程[1].添加护盾[i].挨打方]:护盾更新(self.战斗流程[1].添加护盾[i].护盾)
		end
		self.战斗流程[1].添加护盾.完成 = true
	end
	if self.战斗流程[1].魔法更新~= nil then self.战斗单位[ljcs][self.战斗流程[1].攻击方]:魔法更新(self.战斗流程[1].魔法更新) end
	if self.战斗流程[1].愤怒更新~= nil then self.战斗单位[ljcs][self.战斗流程[1].攻击方]:愤怒更新(self.战斗流程[1].愤怒更新) end
	if self.战斗流程[1].战意更新~= nil then self.战斗单位[ljcs][self.战斗流程[1].攻击方]:战意更新(self.战斗流程[1].战意更新) end
	if self.战斗流程[1].超级战意更新~= nil then self.战斗单位[ljcs][self.战斗流程[1].攻击方]:超级战意更新(self.战斗流程[1].超级战意更新) end
	if self.战斗流程[1].骤雨更新~= nil then self.战斗单位[ljcs][self.战斗流程[1].攻击方]:骤雨更新(self.战斗流程[1].骤雨更新) end
	if self.战斗流程[1].五行珠更新~= nil then self.战斗单位[ljcs][self.战斗流程[1].攻击方]:五行珠更新(self.战斗流程[1].五行珠更新) end
	if self.战斗流程[1].人参果更新~= nil then self.战斗单位[ljcs][self.战斗流程[1].攻击方]:人参果更新(self.战斗流程[1].人参果更新) end
	if self.战斗流程[1].结尾死亡 then
		self.战斗单位[ljcs][self.战斗流程[1].攻击方]:死亡处理1(self.战斗流程[1].结尾死亡)
	end
	table.remove(self.战斗流程,1)
	collectgarbage("step")
	self.执行流程=0
	if #self.战斗流程==0 then
		self.拼接特效=nil
		self.背景状态=nil
		for i=1,#self.战斗单位[ljcs] do
			self.战斗单位[ljcs][i]:回合结束重置()
		end
		self.进程="等待"
		if 引擎.场景.观战中 == false then
			发送数据(5825.1,{self.战斗时间})
		end
	end
	self.进程="计算"
end
local qxbx={}
	qxbx["金刚护法"]=1
	qxbx["分身术"]=1
	qxbx["修罗隐身"]=1
	qxbx["楚楚可怜"]=1
function 战斗类:结束流程(内容)
	for i=1,#内容 do
		for k,v in pairs(内容[i]) do
			if 内容[i].挨打方 ~= nil and self.战斗单位[ljcs][内容[i].挨打方] ~= nil then
				if k == "取消状态" then
					for s=1,#内容[i].取消状态 do
						self.战斗单位[ljcs][内容[i].挨打方].状态特效[内容[i].取消状态[s]]=nil
						if qxbx[内容[i].取消状态[s]] then
							self.战斗单位[ljcs][内容[i].挨打方]:取消变相()
						end
					end
				elseif k == "复活" then
					self.战斗单位[ljcs][内容[i].挨打方].停止更新=false
					self.战斗单位[ljcs][内容[i].挨打方]:换动作("待战")
					self.战斗单位[ljcs][内容[i].挨打方]:设置掉血(内容[i].复活.气血,2)
				elseif k == "法术状态" then
					for z,x in pairs(内容[i].法术状态) do
						if self.战斗单位[ljcs][内容[i].挨打方].状态特效[z] == nil then
							self.战斗单位[ljcs][内容[i].挨打方]:增加状态(z)
							self.战斗单位[ljcs][内容[i].挨打方].状态特效[z].回合 = 内容[i].法术状态[z].回合
						else
							self.战斗单位[ljcs][内容[i].挨打方].状态特效[z].回合 = 内容[i].法术状态[z].回合
						end
						if 内容[i].法术状态[z].层数~=nil then
							self.战斗单位[ljcs][内容[i].挨打方].状态特效[z].层数=内容[i].法术状态[z].层数
						end
					end
				elseif k == "人物状态" then
					self.战斗单位[ljcs][内容[i].挨打方]:结束同步(内容[i].人物状态.气血,内容[i].人物状态.最大气血,内容[i].人物状态.气血上限,内容[i].人物状态.魔法,内容[i].人物状态.最大魔法,内容[i].人物状态.愤怒,内容[i].人物状态.护盾,内容[i].人物状态.战意,内容[i].人物状态.五行珠,内容[i].人物状态.人参果,内容[i].人物状态.骤雨,内容[i].人物状态.超级战意)
				elseif k == "结尾回血" then
					self.战斗单位[ljcs][内容[i].挨打方]:设置掉血(内容[i].结尾回血.气血,2)
				elseif k == "共生" then
					self.战斗单位[ljcs][内容[i].挨打方].共生=true
				end
			end
		end
	end
	for i=1,#self.特殊状态 do
		if self.特殊状态[i] ~= nil then
			if self.特殊状态[i].回合 == 0 then
				self:特殊状态处理(self.特殊状态[i].名称,2)
				self.特殊状态[i] = nil
				table.remove(self.特殊状态,i)
			else
				self.特殊状态[i].回合 = self.特殊状态[i].回合 -1
			end
		end
	end
end

function 战斗类:置全屏技能(jnm,单位)
	if self.战斗单位[ljcs][self.施法信息.攻击方].招式特效 and self.战斗单位[ljcs][self.施法信息.攻击方].招式特效[jnm] then
		jnm = "新_"..jnm
	end

	if jnm == "秘传飞砂走石" then
		self.全屏加速=1
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("秘传飞砂走石")
		self.拼接特效[#self.拼接特效].偏移 = {x=0,y=0}
		qp = true
	elseif jnm == "新_龙吟" then
		self.全屏加速=1
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("新_龙吟")
		self.拼接特效[#self.拼接特效].偏移 = {x=-75,y=-47}
		qp = true
	elseif jnm == "新_破釜沉舟" then
		self.全屏加速=1
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("新_破釜沉舟")
		self.拼接特效[#self.拼接特效].偏移 = {x=-96,y=-34}
		qp = true
	elseif jnm == "破釜沉舟" then
		self.全屏加速=1
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("破釜沉舟")
		self.拼接特效[#self.拼接特效].偏移 = {x=-109,y=-53}
		qp = true
	elseif jnm == "新_推气过宫" then
		self.全屏加速=2.5
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("新_推气过宫")
		self.拼接特效[#self.拼接特效].偏移 = {x=-40,y=50}
		qp = true
	elseif jnm == "新_雷霆万钧" then
		self.全屏加速=1.2
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("新_雷霆万钧")
		self.拼接特效[#self.拼接特效].偏移 = {x=-73,y=-60}
		qp = true
	elseif jnm == "新_落叶萧萧" then
		self.全屏加速=1.2
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("新_落叶萧萧")
		self.拼接特效[#self.拼接特效].偏移 = {x=-93,y=56}
		qp = true
	elseif jnm == "新_翻江搅海" then
		self.全屏加速=2.5
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("新_翻江搅海")
		self.拼接特效[#self.拼接特效].偏移 = {x=-62,y=26}
		qp = true
	elseif jnm == "神针撼海" then
		self.全屏加速=3
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("神针撼海")
		self.拼接特效[#self.拼接特效].偏移 = {x=-55+50,y=15-58}
		qp = true
	elseif jnm == "雨落寒沙" then
		self.拼接特效 = {}
		if 单位.敌我==1 then
			self.拼接特效[1] = {}
			self.拼接特效[1].特效 = 单位:加载特效("雨落寒沙_我方")
			self.拼接特效[1].偏移 = {x=-10,y=-41}
		else
			self.拼接特效[1] = {}
			self.拼接特效[1].特效 = 单位:加载特效("雨落寒沙_敌方")
			self.拼接特效[1].偏移 = {x=-60,y=27}
		end
		self.全屏加速=1.3
		qp = true
	elseif jnm == "瘴气" then
		self.拼接特效 = {}
		if 单位.敌我==1 then
			self.拼接特效[1] = {}
			self.拼接特效[1].特效 = 单位:加载特效("瘴气")
			self.拼接特效[1].偏移 = {x=-50,y=-20}
		else
			self.拼接特效[1] = {}
			self.拼接特效[1].特效 = 单位:加载特效("瘴气")
			self.拼接特效[1].偏移 = {x=-80,y=20}
		end
		self.全屏加速=2
		qp = true
	elseif jnm == "龙卷雨击" then
		self:加载龙卷雨击特效(单位)
	elseif jnm == "龙吟" then
		self:加载龙吟特效(单位)
	elseif jnm == "飞砂走石" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("飞砂走石1")
		self.拼接特效[1].偏移 = {x=-36,y=80}
		self.拼接特效[2] = {}
		self.拼接特效[2].特效 = 单位:加载特效("飞砂走石2")
		self.拼接特效[2].偏移 = {x=-100,y=-15}
		self.拼接特效[3] = {}
		self.拼接特效[3].特效 = 单位:加载特效("飞砂走石2")
		self.拼接特效[3].偏移 = {x=100-36,y=-60}
		self.全屏加速=1.3
		qp = true
	elseif jnm == "风云变色" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("飞砂走石2")
		self.拼接特效[1].偏移 = {x=0,y=50}
		self.拼接特效[2] = {}
		self.拼接特效[2].特效 = 单位:加载特效("飞砂走石1")
		self.拼接特效[2].偏移 = {x=-100,y=-15}
		self.拼接特效[3] = {}
		self.拼接特效[3].特效 = 单位:加载特效("飞砂走石1")
		self.拼接特效[3].偏移 = {x=0,y=10}
		self.拼接特效[4] = {}
		self.拼接特效[4].特效 = 单位:加载特效("飞砂走石2")
		self.拼接特效[4].偏移 = {x=100,y=-50}
		self.全屏加速=1.2
		qp = true
		if 单位.敌我 == 1 then
			self:风云变色处理("风云变色1", 全局游戏宽度-262-((全局游戏宽度-800)/2),全局游戏高度-220-((全局游戏高度-600)/2))
		else
			self:风云变色处理("风云变色2", (全局游戏宽度-800)/2+313,(全局游戏高度-600)/2+236)
		end
	elseif jnm == "其疾如风" or jnm == "其徐如林" or jnm == "侵掠如火" or jnm == "岿然如山" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效(jnm)
		self.拼接特效[1].偏移 = {x=0,y=40}
		self.全屏加速=1
		qp = true
	elseif jnm == "无间地狱" then
		self.全屏加速= 1
		self.拼接偏移.x,self.拼接偏移.y =0,0
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("无间地狱")
		self.拼接特效[1].偏移 = {x=87,y=84}
		self.特殊状态[#self.特殊状态+1] = {名称 ="无间地狱" ,回合=3 }
		self:特殊状态处理("无间地狱",1)
	elseif jnm == "清静菩提" then
		self.全屏加速= 1
		self.拼接偏移.x,self.拼接偏移.y =0,0
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("清静菩提")
		self.拼接特效[1].偏移 = {x=87,y=84}
		self.特殊状态[#self.特殊状态+1] = {名称 ="清静菩提" ,回合=3 }
		self:特殊状态处理("清静菩提",1)
	elseif jnm == "媚眼如丝" then
		self.全屏加速= 1
		self.拼接偏移.x,self.拼接偏移.y =0,0
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("媚眼如丝")
		self.拼接特效[1].偏移 = {x=87,y=84}
		self.特殊状态[#self.特殊状态+1] = {名称 ="媚眼如丝" ,回合=3 }
		self:特殊状态处理("媚眼如丝",1)
	elseif jnm == "诸天看护" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("诸天看护")
		self.拼接特效[1].偏移 = {x=0,y=0}
		self.全屏加速=1
		qp = true
	elseif jnm == "天罚" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("天罚")
		self.拼接特效[1].偏移 = {x=0,y=0}
		self.全屏加速=0.8
		qp = true
	elseif jnm == "法身" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("法身")
		self.拼接特效[1].偏移 = {x=0,y=0}
		self.全屏加速=1
		qp = true
	elseif jnm == "功德无量" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("功德无量")
		self.拼接特效[1].偏移 = {x=87,y=84}
		self.全屏加速=1
		qp = true
	elseif jnm == "吞云吐雾" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("吞云吐雾")
		self.拼接特效[1].偏移 = {x=-100,y=-200}
		self.全屏加速=1
		qp = true
	elseif jnm == "魔神降临" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("魔神降临")
		self.拼接特效[1].偏移 = {x=0,y=0}
		self.全屏加速=1
		qp = true
	elseif jnm == "巨岩破" then
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("巨岩破")
		self.拼接特效[1].偏移 = {x=0,y=0}
		self.全屏加速=1
		qp = true
	elseif jnm == "奔雷咒" then
		self.拼接特效 = {}
		for s=1,8 do
			self.拼接特效[s] = {}
			self.拼接特效[s].特效 = 单位:加载特效("奔雷咒"..s)

			self.拼接特效[s].偏移 = {x=0,y=0}
			if s == 1 then
				self.拼接特效[s].偏移 = {x=-150,y=20}
				self.拼接特效[s].延时=40
			elseif s == 2 then
				self.拼接特效[s].偏移 = {x=10,y=-50}
			elseif s == 3 then
				self.拼接特效[s].偏移 = {x=-60,y=-20}
				self.拼接特效[s].延时=60
			elseif s == 4 then
				self.拼接特效[s].偏移 = {x=-120,y=10}
				self.拼接特效[s].延时=20
			elseif s == 5 then
				self.拼接特效[s].偏移 = {x=30,y=-50}
				self.拼接特效[s].延时=30
			elseif s == 6 then
				self.拼接特效[s].偏移 = {x=-150,y=20}
			elseif s == 7 then
				self.拼接特效[s].偏移 = {x=10,y=-50}
			elseif s == 8 then
				self.拼接特效[s].偏移 = {x=-60,y=-20}
				self.拼接特效[s].延时=40
			end
		end
		self.背景状态 = 1
		self.掉血特效=4
		self.掉血帧=6
		self.全屏加速=1.2
		qp = true
	elseif jnm == "水漫金山" then
		self.全屏加速= 1.5
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("水漫金山2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-50,y=-50}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("水漫金山2")
		self.拼接特效[#self.拼接特效].偏移 = {x=100,y=-100}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("水漫金山2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-185,y=48}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("水漫金山0")
		self.拼接特效[#self.拼接特效].偏移 = {x=-225,y=-34}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("水漫金山0")
		self.拼接特效[#self.拼接特效].偏移 = {x=-30,y=-168}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("水漫金山1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-221,y=-33}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("水漫金山1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-31,y=-169}
		qp = true
	elseif jnm == "泰山压顶" then
		self.拼接特效 = {}
		for s=1,2 do
			self.拼接特效[s] = {}
			self.拼接特效[s].特效 = 单位:加载特效("泰山压顶"..s)
			self.拼接特效[s].偏移 = {x=0,y=0}
			if s == 1 then
				self.拼接特效[s].偏移 = {x=-50,y=10}
				self.拼接特效[s].延时=10
			elseif s == 2 then
				self.拼接特效[s].偏移 = {x=-50,y=10}
			end
		end
		self.全屏加速=3
		self.背景状态 = 2
		self.掉血特效=1
		self.掉血帧=1
		qp = true
	elseif jnm == "地狱烈火" then
		--self:加载地狱烈火特效(单位)
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-290,y=53}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-335,y=-22}
		self.拼接特效[#self.拼接特效].延时=10
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-178,y=126}
		self.拼接特效[#self.拼接特效].延时=10
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-290,y=53}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].延时=30
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-335,y=-22}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-178,y=126}
		self.拼接特效[#self.拼接特效].延时=30
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-155,y=-39}
		self.拼接特效[#self.拼接特效].延时=15
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-207,y=-119}
		self.拼接特效[#self.拼接特效].延时=15
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-41,y=38}
		self.拼接特效[#self.拼接特效].延时=15
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-155,y=-39}
		self.拼接特效[#self.拼接特效].延时=35
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-207,y=-119}
		self.拼接特效[#self.拼接特效].延时=35
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-41,y=38}
		self.拼接特效[#self.拼接特效].延时=35
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-25,y=-106}
		self.拼接特效[#self.拼接特效].延时=25
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-81,y=-195}
		self.拼接特效[#self.拼接特效].延时=25
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=85,y=-40}
		self.拼接特效[#self.拼接特效].延时=25
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-25,y=-106}
		self.拼接特效[#self.拼接特效].延时=45
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=-81,y=-195}
		self.拼接特效[#self.拼接特效].延时=45
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=85,y=-40}
		self.拼接特效[#self.拼接特效].延时=45
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=88,y=-204}
		self.拼接特效[#self.拼接特效].延时=35
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2") --这个可以取消
		self.拼接特效[#self.拼接特效].偏移 = {x=29,y=-255}
		self.拼接特效[#self.拼接特效].延时=35
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火2")
		self.拼接特效[#self.拼接特效].偏移 = {x=201,y=-132}
		self.拼接特效[#self.拼接特效].延时=35
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=88,y=-204}
		self.拼接特效[#self.拼接特效].延时=45
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1") --这个可以取消
		self.拼接特效[#self.拼接特效].偏移 = {x=29,y=-255}
		self.拼接特效[#self.拼接特效].延时=65
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("地狱烈火1")
		self.拼接特效[#self.拼接特效].偏移 = {x=201,y=-132}
		self.拼接特效[#self.拼接特效].延时=45
		self.全屏加速 = 3.25--3.25
	    self.背景状态 = 3
	    self.掉血特效 = 8
	    self.掉血帧 = 10
	    qp = true
	elseif jnm == "天降灵葫_大" then
		self.全屏加速= 1
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效(jnm)
		self.拼接特效[1].偏移 = {x=-50,y=-40}
		qp = true
	elseif jnm == "天降灵葫_中" then
		self.全屏加速= 1
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效(jnm)
		self.拼接特效[1].偏移 = {x=-50,y=-40}
		qp = true
	elseif jnm == "天降灵葫_小" then
		self.全屏加速= 1
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效(jnm)
		self.拼接特效[1].偏移 = {x=-50,y=-40}
		qp = true
	elseif jnm == "八凶法阵" then
		self.全屏加速= 1
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("八凶法阵")
		self.拼接特效[1].偏移 = {x=-50,y=-40}
		qp = true
	elseif jnm == "叱咤风云" then
		self.全屏加速= 1
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("叱咤风云")
		self.拼接特效[1].偏移 = {x=-50,y=-40}
		qp = true
	elseif jnm == "流沙轻音" then
		self.全屏加速= 1
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("流沙轻音")
		self.拼接特效[1].偏移 = {x=-50,y=-40}
		qp = true
	elseif jnm == "刀光剑影" then
		self.全屏加速= 1.48
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("蚩尤技能")
		self.拼接特效[1].偏移 = {x=-135,y=-100}
		for s=2,2 do
			self.拼接特效[s] = {}
			self.拼接特效[s].特效 = 单位:加载特效("刀光剑影")
			self.拼接特效[s].偏移 = {x=-126,y=-30}
		end
		for s=3,3 do
			self.拼接特效[s] = {}
			self.拼接特效[s].特效 = 单位:加载特效("刀光剑影2")
			self.拼接特效[s].偏移 = {x=-360+((s-2)*480-120),y=250-175-((s-2)*75)}
			self.拼接特效[#self.拼接特效].延时=100
		end
		qp = true
	elseif jnm == "毁灭之光" then
		self.全屏加速= 1.48
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("蚩尤技能")
		self.拼接特效[1].偏移 = {x=-135,y=-100}
		for s=2,2 do
			self.拼接特效[s] = {}
			self.拼接特效[s].特效 = 单位:加载特效("毁灭之光")
			self.拼接特效[s].偏移 = {x=-126,y=-30}
		end
		qp = true
	elseif jnm == "武神之怒" then
		self.全屏加速= 1.48
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("蚩尤技能")
		self.拼接特效[1].偏移 = {x=-135,y=-100}
		for s=2,2 do
			self.拼接特效[s] = {}
			self.拼接特效[s].特效 = 单位:加载特效("武神之怒")
			self.拼接特效[s].偏移 = {x=-126,y=-30}
		end
		qp = true
	elseif jnm == "翻江搅海" then
		self.全屏加速= 1
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("翻江搅海")
		self.拼接特效[1].偏移 = {x=-55,y=15}
		qp = true
	elseif jnm == "魔火焚世" then
		self.全屏加速= 1
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("魔火焚世")
		if 单位.敌我==1 then
			self.拼接特效[1].偏移 = {x=20,y=0}
		else
			self.拼接特效[1].偏移 = {x=-40,y=-10}
		end
		qp = true
	elseif jnm == "云暗天昏"  then
		self.全屏加速= 1
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("云暗天昏")
		self.拼接特效[1].偏移 = {x=-55,y=15}
		qp = true
	elseif jnm == "枯木逢春" then
		self.全屏加速= 0.7
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("枯木逢春1")
		self.拼接特效[#self.拼接特效].偏移 = {x=66-100,y=-28}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("枯木逢春2")
		self.拼接特效[#self.拼接特效].偏移 = {x=5-70,y=9+50}
		self.拼接特效[#self.拼接特效].延时=40
		qp = true
	elseif jnm == "蛊木迷瘴" then
		self.全屏加速= 0.7
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("蛊木迷瘴")
		self.拼接特效[#self.拼接特效].偏移 = {x=66-100,y=-28+50}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("蛊木迷瘴2")
		self.拼接特效[#self.拼接特效].偏移 = {x=5-100,y=9+50}
		self.拼接特效[#self.拼接特效].延时=40
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("蛊木迷瘴2")
		self.拼接特效[#self.拼接特效].偏移 = {x=77-100,y=-5+50}
		self.拼接特效[#self.拼接特效].延时=40
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("蛊木迷瘴2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-61-100,y=-29+50}
		self.拼接特效[#self.拼接特效].延时=60
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("蛊木迷瘴2")
		self.拼接特效[#self.拼接特效].偏移 = {x=138-100,y=-23+50}
		self.拼接特效[#self.拼接特效].延时=60
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("蛊木迷瘴2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-109-100,y=-62+50}
		self.拼接特效[#self.拼接特效].延时=90
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("蛊木迷瘴2")
		self.拼接特效[#self.拼接特效].偏移 = {x=22-100,y=-31+50}
		self.拼接特效[#self.拼接特效].延时=80
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("蛊木迷瘴1")
		self.拼接特效[#self.拼接特效].偏移 = {x=162-100,y=-110+50}
		self.拼接特效[#self.拼接特效].延时=90
		qp = true
	elseif jnm == "落叶萧萧" or jnm == "风卷残云" or jnm == "疾风秋叶"  then
		self.全屏加速= 0.7
		self.拼接特效 = {}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("落叶萧萧")
		self.拼接特效[#self.拼接特效].偏移 = {x=66-50,y=-28+40}
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("落叶萧萧2")
		self.拼接特效[#self.拼接特效].偏移 = {x=5-50,y=9+40}
		self.拼接特效[#self.拼接特效].延时=40
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("落叶萧萧2")
		self.拼接特效[#self.拼接特效].偏移 = {x=77-50,y=-5+40}
		self.拼接特效[#self.拼接特效].延时=40
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("落叶萧萧2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-61-50,y=-29+40}
		self.拼接特效[#self.拼接特效].延时=60
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("落叶萧萧2")
		self.拼接特效[#self.拼接特效].偏移 = {x=138-50,y=-23+40}
		self.拼接特效[#self.拼接特效].延时=60
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("落叶萧萧2")
		self.拼接特效[#self.拼接特效].偏移 = {x=-109-50,y=-62+40}
		self.拼接特效[#self.拼接特效].延时=90
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("落叶萧萧2")
		self.拼接特效[#self.拼接特效].偏移 = {x=22-50,y=-31+40}
		self.拼接特效[#self.拼接特效].延时=80
		self.拼接特效[#self.拼接特效+1] = {}
		self.拼接特效[#self.拼接特效].特效 = 单位:加载特效("落叶萧萧1")
		self.拼接特效[#self.拼接特效].偏移 = {x=162-50,y=-110+40}
		self.拼接特效[#self.拼接特效].延时=90
		qp = true
	elseif jnm == "扶摇万里" then
		self.全屏加速= 0.7
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("扶摇万里")
		self.拼接特效[1].偏移 = {x=-40,y=-40}
		self.背景状态 = 1
		qp = true
	elseif jnm == "北冥之渊" then
		self.全屏加速= 0.7
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("北冥之渊")
		self.拼接特效[1].偏移 = {x=-325,y=-110}
		qp = true
	elseif jnm == "天地洞明" then
		self.全屏加速= 0.7
		self.拼接特效 = {}
		self.拼接特效[1] = {}
		self.拼接特效[1].特效 = 单位:加载特效("天地洞明")
		self.拼接特效[1].偏移 = {x=-127,y=-100}
		qp = true
		self.天地洞明=true
	end
	self.全屏加速=self.全屏加速+0.2
	if qp then
		if 单位.敌我 == 1 then
			self.拼接偏移.x,self.拼接偏移.y = 全局游戏宽度-200-((全局游戏宽度-800)/2),全局游戏高度-200-((全局游戏高度-600)/2)
		else
			self.拼接偏移.x,self.拼接偏移.y = (全局游戏宽度-800)/2+313,(全局游戏高度-600)/2+236
		end
	end
end



function 战斗类:加载龙卷雨击特效(单位)
    self.拼接特效 = {}
    local 偏移和延时 = {
        {x = 66, y = -100},    -- 第1组 36帧
        {x = 106, y = -102},   -- 第2组 51帧
        {x = 15, y = -3},      -- 第3组 51帧
        {x = -57, y = 61},     -- 第4组 51帧
        {x = -146, y = -84},   -- 第5组 51帧
        {x = -196, y = 144},   -- 第6组 51帧
        {x = -103, y = 45},    -- 第7组(大水浪) 41帧
        {x = -38, y = 29, 延时=30},     -- 第8组(烟囱) 33帧
        {x = -33, y = -51, 延时=90}     -- 第9组(水龙) 18帧
    }

    for s = 1, #偏移和延时 do
        local offset = 偏移和延时[s]
        self.拼接特效[s] = {
            特效 = 单位:加载特效("龙卷雨击" .. s),
            偏移 = {x = offset.x, y = offset.y},
            延时 = offset.延时
        }
    end

    self.背景状态 = 1
    self.掉血特效 = 8
    self.掉血帧 = 24
    self.全屏加速 = 1.35
    qp = true
end
function 战斗类:加载龙吟特效(单位)
    self.拼接特效 = {}
    local 偏移和延时 = {
        {x = -26, y = 15, 延时 = 10},
        {x = 124, y = -36},
        {x = -213, y = 24},
        {x = -44, y = -50},
        {x = -35, y = -62, 延时 = 10},
        {x = -33, y = 13, 延时 = 50},
        {x = 41, y = -28, 延时 = 50},
        {x = -89, y = -35, 延时 = 50},
        {x = -153, y = -2, 延时 = 50},
        {x = 0, y = -40}
    }

    for s = 1, #偏移和延时 do
        local offset = 偏移和延时[s]
        self.拼接特效[s] = {
            特效 = 单位:加载特效("龙吟" .. s),
            偏移 = {x = offset.x, y = offset.y},
            延时 = offset.延时
        }
    end

    self.背景状态 = 1
    self.掉血特效 = 5
    self.掉血帧 = 6
    self.全屏加速 = 1.8
    qp = true
end
--======================7.21修改
function 战斗类:风云变色处理(名称,x,y)
	if 名称 == "风云变色1" then
		self.色变动画我方 = {动画=引擎.场景.资源:载入('waddon.wdf',"网易WDF动画",1514813037),x=x,y=y}
		self.色变进度我方 = 255
		self.色变我方 = os.time()+ 2
	elseif 名称 == "风云变色2" then
		self.色变动画敌方 = {动画=引擎.场景.资源:载入('waddon.wdf',"网易WDF动画",1514813037),x=x,y=y}
		self.色变进度敌方 = 255
		self.色变敌方 = os.time()+ 2
	end
end

function 战斗类:特殊状态处理(名称,类型,x,y)
	if 名称 == "无间地狱" then
		if 类型 == 1 then
			self.无间地狱 = 引擎.场景.资源:载入('waddon.wdf',"网易WDF动画",2808758193)
			self.无间地狱死亡 = 引擎.场景.资源:载入('waddon.wdf',"网易WDF动画",2289147609)
			self.无间地狱开关=nil
		else
			self.无间地狱 = nil
			self.无间地狱死亡 = nil
			self.无间地狱开关=nil
		end
	elseif 名称 == "媚眼如丝" then
		if 类型 == 1 then
			self.媚眼如丝 = {动画=引擎.场景.资源:载入('waddon.wdf',"网易WDF动画",3883598561),x=x,y=y}
			self.媚眼如丝回血 = {动画=引擎.场景.资源:载入('waddon.wdf',"网易WDF动画",2565398678),x=x,y=y}
			self.媚眼如丝开关 = nil
		else
			self.媚眼如丝 = nil
			self.媚眼如丝回血 = nil
			self.媚眼如丝开关 = nil
		end
	elseif 名称 == "清静菩提" then
		if 类型 == 1 then
			self.清静菩提 = {动画=引擎.场景.资源:载入('waddon.wdf',"网易WDF动画",1772528793),x=x,y=y}
			self.清静菩提动画 = {动画=引擎.场景.资源:载入('waddon.wdf',"网易WDF动画",1458710428),x=x,y=y}
			self.清静菩提开关 = nil
		else
			self.清静菩提 = nil
			self.清静菩提动画 = nil
			self.清静菩提开关 = nil
		end
	end
end

function 战斗类:添加战斗提醒文字(q)
	self.战斗信息提示.开关=true
	self.战斗信息提示.文字=q
	self.战斗信息提示.起始时间=os.time()
end
function 战斗类:显示(dt,x,y)

	self.纯色背景:显示(0,0)
	if self.无间地狱 == nil and self.媚眼如丝 == nil and self.清静菩提 == nil then
		self.战斗背景:显示(全局游戏宽度/2-235,全局游戏高度/2-125)
	else
		if self.无间地狱 ~= nil then
			self.无间地狱:更新()
			self.无间地狱:显示(全局游戏宽度/2,全局游戏高度/2+30)
			if self.无间地狱开关 then
				self.无间地狱死亡:更新()
				self.无间地狱死亡:显示(全局游戏宽度/2,全局游戏高度/2+30)
				if self.无间地狱死亡.当前帧>=self.无间地狱死亡.结束帧 then
					self.无间地狱死亡.当前帧=0
					self.无间地狱开关=nil
				end
			end
		end
		if self.媚眼如丝 ~= nil then
			self.媚眼如丝.动画:更新()
			self.媚眼如丝.动画:显示(全局游戏宽度/2,全局游戏高度/2+30)
			if self.媚眼如丝开关 then
				self.媚眼如丝回血.动画:更新()
				self.媚眼如丝回血.动画:显示(全局游戏宽度/2,全局游戏高度/2+30)
				if self.媚眼如丝回血.动画.当前帧>=self.媚眼如丝回血.动画.结束帧 then
					self.媚眼如丝回血.动画.当前帧=0
					self.媚眼如丝开关=nil
				end
			end
		end
		if self.清静菩提 ~= nil then
			self.清静菩提.动画:更新()
			self.清静菩提.动画:显示(全局游戏宽度/2,全局游戏高度/2+30)
			if self.清静菩提开关 then
				self.清静菩提动画.动画:更新()
				self.清静菩提动画.动画:显示(全局游戏宽度/2,全局游戏高度/2+30)
				if self.清静菩提动画.动画.当前帧>=self.清静菩提动画.动画.结束帧 then
					self.清静菩提动画.动画.当前帧=0
					self.清静菩提开关=nil
				end
			end
		end
	end
	if self.法阵能量  then
		self.能量条背景:显示(全局游戏宽度/2-240+169,全局游戏高度-98)
		self.能量条:置区域(0,0,math.min(math.floor(self.法阵能量 / 100 * 159),159),16)
		self.能量条:显示(全局游戏宽度/2-235+169,全局游戏高度-92)
	end
	if self.色变我方 ~= nil then
		if os.time() - self.色变我方 >=0  then
			self.色变动画我方.动画:更新()
			self.色变进度我方 = self.色变进度我方 - 0.1
			self.色变动画我方.动画:置颜色(ARGB(self.色变进度我方,255,255,255))
			self.色变动画我方.动画:显示(self.色变动画我方.x,self.色变动画我方.y)
			if self.色变进度我方<=100 then
				self.色变动画我方=nil
				self.色变进度我方=nil
				self.色变我方=nil
			end
		end
	end
	if self.色变敌方 ~= nil then
		if os.time() - self.色变敌方 >=0  then
			self.色变动画敌方.动画:更新()
			self.色变进度敌方 = self.色变进度敌方 - 0.1
			self.色变动画敌方.动画:置颜色(ARGB(self.色变进度敌方,255,255,255))
			self.色变动画敌方.动画:显示(self.色变动画敌方.x,self.色变动画敌方.y)
			if self.色变进度敌方<=100 then
				self.色变动画敌方=nil
				self.色变进度敌方=nil
				self.色变敌方=nil
			end
		end
	end
	if self.背景状态 == 1 then
		self.黑幕背景:显示(0,0)
	elseif self.背景状态 == 2 then
		self.蓝色背景:显示(0,0)
	elseif self.背景状态 == 3 then
		self.红色背景:显示(0,0)
	end
	if self.PK战斗 then
		self.竖条:更新(x,y)
		self.横条:更新(x,y)
		self.竖条:显示(837+全局游戏宽度-850,180)
		self.横条:显示(230,0)
		if self.竖条:是否选中(x,y) and 引擎.鼠标弹起(左键) then
			if self.我方头像开关 then
				self.我方头像开关=false
			else
				self.我方头像开关=true
			end
		end
		if self.横条:是否选中(x,y) and 引擎.鼠标弹起(左键) then
			if self.敌方头像开关 then
				self.敌方头像开关=false
			else
				self.敌方头像开关=true
			end
		end
		if self.我方头像开关 and  self.进程=="命令" then
			for i=1,#self.我方头像组 do
				if 全局界面风格=="国韵" then
					tp.人物头像背景_:显示(全局游戏宽度-67-16,全局游戏高度/2-180+(i-1)*55)
				else
					tp.人物头像背景_:显示(全局游戏宽度-67,全局游戏高度/2-180+(i-1)*55)
				end
				self.我方头像组[i]:更新(x,y)
				self.我方头像组[i]:显示(全局游戏宽度-64,全局游戏高度/2-177+(i-1)*55,true)
				if self.我方头像组[i]:是否选中(x,y) then
					tp.提示:自定义(全局游戏宽度-64,全局游戏高度/2-133+(i-1)*55,"#Y/名称:"..self.我方头像组[i].名称.."\n#Y/等级:"..(self.我方头像组[i].等级 or 0).."\n#Y/门派:"..(self.我方头像组[i].门派 or "无"),93)
				end
			end
		end
		if self.敌方头像开关 and  self.进程=="命令" then
			for i=1,#self.敌方头像组 do
				if 全局界面风格=="国韵" then
					tp.人物头像背景_:显示(全局游戏宽度/2-157+(i-1)*55-16,23)
				else
					tp.人物头像背景_:显示(全局游戏宽度/2-157+(i-1)*55,23)
				end
				self.敌方头像组[i]:更新(x,y)
				self.敌方头像组[i]:显示(全局游戏宽度/2-154+(i-1)*55,26,true)
				if self.敌方头像组[i]:是否选中(x,y) then
					tp.提示:自定义(全局游戏宽度/2-194+(i-1)*55,76,"#Y/名称:"..self.敌方头像组[i].名称.."\n#Y/等级:"..(self.敌方头像组[i].等级 or 0).."\n#Y/门派:"..(self.敌方头像组[i].门派 or "无"))
				end
			end
		end
		if self.单挑模式 and self.进程=="命令" then
			self.战斗指令:单挑显示(dt,x,y)
		end
		if (self.技能待放组[1]:是否选中(x,y) or self.技能待放组[2]:是否选中(x,y)) and 引擎.鼠标弹起(左键) and not self.单挑模式 then
			if self.技能展示开关 then
				self.技能展示开关=false
			else
				self.技能展示开关=true
			end
		end
	end
	self.阵型组[1]:显示(全局游戏宽度-26,100)
	for i=1,#self.战斗单位[ljcs] do
		if self.战斗单位[ljcs][i].初始化结束 == nil then return end
		self.显示排序[i] = {显示y=self.战斗单位[ljcs][i].显示xy.y,编号=i}
	end
	table.sort(self.显示排序,排序)
	for n=1,#self.战斗单位[ljcs] do
		self.战斗单位[ljcs][self.显示排序[n].编号]:显示(dt,x,y)
		if 引擎.鼠标弹起(右键) and self.战斗单位[ljcs][n]:是否选中(x,y) and not tp.窗口.战斗状态栏.可视 then
			if next(self.战斗单位[ljcs][n].状态特效)~=nil or self.战斗单位[ljcs][n].战意>0 or self.战斗单位[ljcs][n].五行珠>0 or self.战斗单位[ljcs][n].人参果.枚>0 or self.战斗单位[ljcs][n].骤雨.层数>0 then
				tp.窗口.战斗状态栏:打开(n)
			end
		end
	end
	if self.排列命令 and #self.排列命令>0 and self.进程~="命令" then
		self:设置命令回合(self.排列命令[1])
		table.remove(self.排列命令,1)
	end
	if self.进程 == "命令" then
		self.战斗指令:显示(dt, x, y)
		self.秒显示 = 0
		self.分显示 = 0
		self.结果 = 时间 - self.命令数据.计时
		self.显示时间 = 0
		if self.结果 >= 1 then
			self.命令数据.计时 = 时间
			self.命令数据.秒 = self.命令数据.秒 - 1
			if self.命令数据.秒 < 0 then
				if self.命令数据.分 <= 0 and self.命令数据.秒 <= 0 then
					self.战斗指令:结束()
					self.进程 = "等待"
					self.显示时间 = 1
				elseif self.命令数据.秒 <= 0 then
					self.命令数据.秒 = 9
					self.命令数据.分 = self.命令数据.分 - 1
				end
			end
		end
		if self.显示时间 == 0 then
			self.分显示 = self.命令数据.分 + 1
			if self.分显示 > 10 then self.分显示 = 1 end
			self.秒显示 = self.命令数据.秒 + 1
			if self.秒显示 > 10 then self.秒显示 = 1 end
			self.数字图片[self.分显示]:显示((全局游戏宽度 / 2) - 45, 15)
			self.数字图片[self.秒显示]:显示((全局游戏宽度 / 2) + 5, 15)
		end
		if self.提示 ~= "" and self.提示 ~= nil then
			引擎.场景.提示:战斗提示(x, y, self.提示)
		end
	else
	end
	if self.进程=="执行" then
		if self.战斗信息提示.开关 then
			self.战斗信息提示.字体:显示(全局游戏宽度/2-75,全局游戏高度-100,self.战斗信息提示.文字)
			if os.time()-self.战斗信息提示.起始时间 >=2 then
				self.战斗信息提示.开关 =false
			end
		end
		if self.拼接特效 ~= nil then
			for n=1,#self.拼接特效 do
				if self.拼接特效[n] ~= nil and self.拼接特效[n].延时==nil then
					self.拼接特效[n].特效:显示(self.拼接偏移.x + self.拼接特效[n].偏移.x ,self.拼接偏移.y + self.拼接特效[n].偏移.y)
				end
			end
			if self.是否拼接 ~= true then
				self.是否拼接 = true
			end
		end
	end
	for n=1,#self.战斗单位[ljcs] do
		if self.战斗单位[ljcs][n].掉血开关 then
			if self.战斗单位[ljcs][n].伤害类型==1 then
				self.战斗单位[ljcs][n]:掉血显示()
			elseif self.战斗单位[ljcs][n].伤害类型==3 or self.战斗单位[ljcs][n].伤害类型==4  then
				self.战斗单位[ljcs][n]:暴击显示()
			elseif self.战斗单位[ljcs][n].伤害类型==3.5 then
				self.战斗单位[ljcs][n]:法术暴击显示()
			else
				self.战斗单位[ljcs][n]:加血显示()
			end
		end
		if self.战斗单位[ljcs][n].其他掉血开关 then
			self.战斗单位[ljcs][n]:其他掉血显示()
		elseif self.战斗单位[ljcs][n].掉血多段开关 then
			self.战斗单位[ljcs][n]:多段掉血显示()
		end
		if __gge.isdebug then
			tp.字体表.华康字体:置颜色(红色):显示(self.战斗单位[ljcs][n].显示xy.x,self.战斗单位[ljcs][n].显示xy.y-100,self.战斗单位[ljcs][n].排序位置)
			tp.字体表.华康字体:置颜色(绿色):显示(self.战斗单位[ljcs][n].显示xy.x-30,self.战斗单位[ljcs][n].显示xy.y-10,self.战斗单位[ljcs][n].显示xy.x..","..self.战斗单位[ljcs][n].显示xy.y)
		end
	end
	if self.自动开关 then
		if self.自动栏.可视化 == false then
			self.自动栏.可视化 = true
		end
		if self.自动栏:检查点(x,y) then
			self.自动栏.鼠标=true
		else
			self.自动栏.鼠标=false
		end
		self.自动栏:显示(dt,x,y)
		if mousea(0) and self.自动栏.可移动 and not 引擎.场景.消息栏焦点 then
			self.自动栏:初始移动(x,y)
		elseif mouseb(0)  or self.隐藏UI or self.消息栏焦点 then
			引擎.场景.场景.战斗.移动窗口 = false
		end
		if 引擎.场景.场景.战斗.移动窗口 and not self.消息栏焦点 then
			self.自动栏:开始移动(x,y)
		end
	end
	if self.观战开关 then
		self.观战栏.可视 = true
		self.观战栏.鼠标=false
		self.观战栏:检查点(x,y)
		self.观战栏:显示(dt,x,y)
	end
	if self.过度精灵 ~= nil then
		self.过度时间 = self.过度时间 - 0.55
		if self.过度时间 <= 0 then
			self.过度进度 = self.过度进度 - (10)
			self.过度时间 = 0
			if self.过度进度 <= 0 then
				self.过度进度 = 0
			end
		end
		self.过度坐标1.x,self.过度坐标1.y = self.过度坐标1.x -6,self.过度坐标1.y -6
		self.过度坐标2.x,self.过度坐标2.y = self.过度坐标2.x +6,self.过度坐标2.y -6
		self.过度坐标3.x,self.过度坐标3.y = self.过度坐标3.x -6,self.过度坐标3.y +6
		self.过度坐标4.x,self.过度坐标4.y = self.过度坐标4.x +6,self.过度坐标4.y +6
		self.过度精灵1:显示(self.过度坐标1.x,self.过度坐标1.y)
		self.过度精灵2:显示(self.过度坐标2.x,self.过度坐标2.y)
		self.过度精灵3:显示(self.过度坐标3.x,self.过度坐标3.y)
		self.过度精灵4:显示(self.过度坐标4.x,self.过度坐标4.y)
		if self.过度进度 <= 0 then
			self.过度精灵 = nil
			self.过度精灵1= nil
			self.过度精灵2= nil
			self.过度精灵3= nil
			self.过度精灵4= nil
			self.过度减少 = nil
		end
	end
	if tp.窗口.战斗状态栏.可视 then
		tp.窗口.战斗状态栏:显示(dt,x,y)
	end
	if __gge.isdebug and self.进程=="命令" then
		local xx = 0
		local yy = 0
		for i=1,#self.怪物头像组 do
			if 全局界面风格=="国韵" then
				tp.宠物头像背景_:显示(10+xx*40-16,100+yy*40)
			else
				tp.宠物头像背景_:显示(10+xx*40,100+yy*40)
			end
			self.怪物头像组[i]:更新(x,y)
			self.怪物头像组[i]:显示(12+xx*40,102+yy*40)
			tp.字体表.冷却字体:显示(10+xx*40,100+yy*40,i)
			if self.怪物头像组[i]:是否选中(x,y) then
				tp.提示:自定义(12+xx*40,102+yy*40,"#Y/名称:"..self.怪物头像组[i].名称.."\n#Y/等级:"..(self.怪物头像组[i].等级 or 0).."\n#Y/气血:"..(self.怪物头像组[i].气血 or 0).."/"..(self.怪物头像组[i].最大气血 or 0).."\n#Y/伤害:"..(self.怪物头像组[i].伤害 or 0).."\n#Y/法伤:"..(self.怪物头像组[i].法伤 or 0).."\n#Y/防御:"..(self.怪物头像组[i].防御 or 0).."\n#Y/法防:"..(self.怪物头像组[i].法防 or 0).."\n#Y/速度:"..(self.怪物头像组[i].速度 or 0))
			end
			yy = yy + 1
			if yy == 5 then
				yy = 0
				xx = xx + 1
			end
		end
	end
end
function 战斗类:重置显示位置(阵法,敌我)
	if 敌我 == 1 then
		if 阵法 == "风扬阵" then
			显示顺序 = {10,8,6,7,9,4,5,2,1,3}
		elseif 阵法 == "地载阵" then
			显示顺序 = {10,8,6,7,9,3,2,1,5,4}
		elseif 阵法 == "龙飞阵" then
			显示顺序 = {10,8,6,7,9,3,5,1,2,4}
		elseif 阵法 == "蛇蟠阵" then
			显示顺序 = {10,8,6,7,9,3,1,5,4,2}
		elseif 阵法 == "鹰啸阵" then
			显示顺序 = {10,8,6,7,9,2,4,1,5,3}
		elseif 阵法 == "雷绝阵" then
			显示顺序 = {10,8,6,7,9,5,3,1,4,2}
		end
	end
end
function 战斗类:加载阵法()
	self.敌方位置={
		[1]={["y"]=192,["x"]=347},
		[3]={["y"]=155,["x"]=410},
		[2]={["y"]=230,["x"]=280},
		[5]={["y"]=119,["x"]=477},
		[4]={["y"]=265,["x"]=211},
		[6]={["y"]=233,["x"]=398},
		[7]={["y"]=268,["x"]=333},
		[8]={["y"]=195,["x"]=463},
		[9]={["y"]=304,["x"]=267},
		[10]={["y"]=160,["x"]=528},
		[11]={["y"]=280,["x"]=460},
	}
	self.我方位置={
		[1]={["y"]=421,["x"]=709},
		[2]={["y"]=450,["x"]=640},
		[3]={["y"]=380,["x"]=770},
		[4]={["y"]=485,["x"]=575},
		[5]={["y"]=345,["x"]=835},
		[6]={["y"]=355,["x"]=635},
		[7]={["y"]=390,["x"]=570},
		[8]={["y"]=320,["x"]=700},
		[9]={["y"]=425,["x"]=505},
		[10]={["y"]=285,["x"]=765}
	}

	self.阵型位置={}
	self.阵型位置.天覆阵={
	我方={
		[1]={["y"]=464,["x"]=758},
		[2]={["y"]=445,["x"]=645},
		[3]={["y"]=385,["x"]=765},
		[4]={["y"]=485,["x"]=585},
		[5]={["y"]=355,["x"]=825},
		[6]={["y"]=355,["x"]=635},
		[7]={["y"]=385,["x"]=575},
		[8]={["y"]=325,["x"]=695},
		[9]={["y"]=415,["x"]=515},
		[10]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=183,["x"]=310},
		[2]={["y"]=240,["x"]=295},
		[3]={["y"]=180,["x"]=415},
		[4]={["y"]=270,["x"]=235},
		[5]={["y"]=150,["x"]=475},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520},
		[11]={["y"]=120,["x"]=225}}}
	self.阵型位置.云垂阵={
	我方={[1]={["y"]=499,["x"]=808},
		[3]={["y"]=445,["x"]=645},
		[2]={["y"]=385,["x"]=765},
		[5]={["y"]=475,["x"]=585},
		[4]={["y"]=355,["x"]=825},
		[6]={["y"]=355,["x"]=635},
		[8]={["y"]=385,["x"]=575},
		[7]={["y"]=325,["x"]=695},
		[10]={["y"]=415,["x"]=515},
		[9]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=156,["x"]=269},
		[2]={["y"]=240,["x"]=295},
		[3]={["y"]=180,["x"]=415},
		[4]={["y"]=270,["x"]=235},
		[5]={["y"]=150,["x"]=475},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520}}}
	self.阵型位置.风扬阵={
	我方={[1]={["y"]=483,["x"]=776},
		[2]={["y"]=451,["x"]=845},
		[3]={["y"]=526,["x"]=701},
		[4]={["y"]=390,["x"]=769},
		[5]={["y"]=454,["x"]=648},
		[6]={["y"]=355,["x"]=635},
		[8]={["y"]=385,["x"]=575},
		[7]={["y"]=325,["x"]=695},
		[10]={["y"]=415,["x"]=515},
		[9]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=189,["x"]=287},
		[2]={["y"]=214,["x"]=220},
		[3]={["y"]=143,["x"]=352},
		[4]={["y"]=248,["x"]=280},
		[5]={["y"]=191,["x"]=406},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520}}}
	self.阵型位置.虎翼阵={
	我方={[1]={["y"]=481,["x"]=801},
		[2]={["y"]=400,["x"]=803},
		[3]={["y"]=466,["x"]=679},
		[4]={["y"]=329,["x"]=805},
		[5]={["y"]=460,["x"]=568},
		[6]={["y"]=355,["x"]=635},
		[8]={["y"]=385,["x"]=575},
		[7]={["y"]=325,["x"]=695},
		[10]={["y"]=415,["x"]=515},
		[9]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=142,["x"]=257},
		[2]={["y"]=209,["x"]=230},
		[3]={["y"]=146,["x"]=365},
		[4]={["y"]=275,["x"]=223},
		[5]={["y"]=160,["x"]=459},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520}}}
	self.阵型位置.鸟翔阵={
	我方={[1]={["y"]=438,["x"]=732},
		[2]={["y"]=433,["x"]=864},
		[3]={["y"]=516,["x"]=690},
		[4]={["y"]=341,["x"]=813},
		[5]={["y"]=480,["x"]=568},
		[6]={["y"]=355,["x"]=635},
		[8]={["y"]=385,["x"]=575},
		[7]={["y"]=325,["x"]=695},
		[10]={["y"]=415,["x"]=515},
		[9]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=203,["x"]=347},
		[2]={["y"]=196,["x"]=239},
		[3]={["y"]=136,["x"]=369},
		[4]={["y"]=275,["x"]=223},
		[5]={["y"]=154,["x"]=470},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520}} }
	self.阵型位置.地载阵={
	我方={[1]={["y"]=464,["x"]=754},
		[2]={["y"]=412,["x"]=689},
		[3]={["y"]=437,["x"]=813},
		[4]={["y"]=488,["x"]=674},
		[5]={["y"]=525,["x"]=821},
		[6]={["y"]=355,["x"]=635},
		[8]={["y"]=385,["x"]=575},
		[7]={["y"]=325,["x"]=695},
		[10]={["y"]=415,["x"]=515},
		[9]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=167,["x"]=306},
		[2]={["y"]=211,["x"]=355},
		[3]={["y"]=200,["x"]=242},
		[4]={["y"]=134,["x"]=376},
		[5]={["y"]=121,["x"]=251},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520}}}
	self.阵型位置.龙飞阵={
	我方={[1]={["y"]=468,["x"]=753},
		[2]={["y"]=512,["x"]=810},
		[3]={["y"]=347,["x"]=808},
		[4]={["y"]=439,["x"]=618},
		[5]={["y"]=414,["x"]=696},
		[6]={["y"]=355,["x"]=635},
		[10]={["y"]=385,["x"]=575},
		[8]={["y"]=325,["x"]=695},
		[9]={["y"]=415,["x"]=515},
		[7]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=174,["x"]=277},
		[2]={["y"]=124,["x"]=215},
		[3]={["y"]=269,["x"]=223},
		[4]={["y"]=168,["x"]=407},
		[5]={["y"]=212,["x"]=330},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520}}}
	self.阵型位置.蛇蟠阵={
	我方={[1]={["y"]=468,["x"]=753},
		[2]={["y"]=505,["x"]=682},
		[3]={["y"]=448,["x"]=820},
		[4]={["y"]=439,["x"]=618},
		[5]={["y"]=535,["x"]=826},
		[6]={["y"]=355,["x"]=635},
		[7]={["y"]=385,["x"]=575},
		[8]={["y"]=325,["x"]=695},
		[9]={["y"]=415,["x"]=515},
		[10]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=174,["x"]=277},
		[2]={["y"]=132,["x"]=351},
		[3]={["y"]=218,["x"]=201},
		[4]={["y"]=168,["x"]=407},
		[5]={["y"]=128,["x"]=223},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520}}
	}
	self.阵型位置.鹰啸阵={
	我方={[1]={["y"]=468,["x"]=753},
		[2]={["y"]=444,["x"]=623},
		[3]={["y"]=381,["x"]=759},
		[4]={["y"]=416,["x"]=688},
		[5]={["y"]=532,["x"]=826},
		[6]={["y"]=355,["x"]=635},
		[7]={["y"]=385,["x"]=575},
		[8]={["y"]=325,["x"]=695},
		[9]={["y"]=415,["x"]=515},
		[10]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=174,["x"]=277},
		[2]={["y"]=168,["x"]=408},
		[3]={["y"]=247,["x"]=274},
		[4]={["y"]=211,["x"]=341},
		[5]={["y"]=128,["x"]=223},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520}}
	}
	self.阵型位置.雷绝阵={
	我方={[1]={["y"]=529,["x"]=824},
		[2]={["y"]=507,["x"]=705},
		[3]={["y"]=449,["x"]=833},
		[4]={["y"]=445,["x"]=631},
		[5]={["y"]=379,["x"]=763},
		[6]={["y"]=355,["x"]=635},
		[7]={["y"]=385,["x"]=575},
		[8]={["y"]=325,["x"]=695},
		[9]={["y"]=415,["x"]=515},
		[10]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=130,["x"]=234},
		[2]={["y"]=131,["x"]=335},
		[3]={["y"]=181,["x"]=234},
		[4]={["y"]=170,["x"]=401},
		[5]={["y"]=233,["x"]=289},
		[6]={["y"]=250,["x"]=400},
		[7]={["y"]=280,["x"]=340},
		[8]={["y"]=220,["x"]=460},
		[9]={["y"]=310,["x"]=280},
		[10]={["y"]=190,["x"]=520}}
	}
	self.阵型位置.boss阵法={我方={[1]={["y"]=464,["x"]=758},[2]={["y"]=445,["x"]=645},[3]={["y"]=385,["x"]=765},[4]={["y"]=475,["x"]=585},[5]={["y"]=355,["x"]=825},[6]={["y"]=355,["x"]=635},[7]={["y"]=385,["x"]=575},[8]={["y"]=325,["x"]=695},[9]={["y"]=415,["x"]=515},[10]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=183,["x"]=310},[2]={["y"]=240,["x"]=295},[3]={["y"]=180,["x"]=415},[4]={["y"]=270,["x"]=235},[5]={["y"]=150,["x"]=475},[6]={["y"]=250,["x"]=400},[7]={["y"]=280,["x"]=340},[8]={["y"]=220,["x"]=460},[9]={["y"]=310,["x"]=280},[10]={["y"]=190,["x"]=520}}}
	self.阵型位置.烛龙阵法={我方={[1]={["y"]=464,["x"]=758},[2]={["y"]=445,["x"]=645},[3]={["y"]=385,["x"]=765},[4]={["y"]=475,["x"]=585},[5]={["y"]=355,["x"]=825},[6]={["y"]=355,["x"]=635},[7]={["y"]=385,["x"]=575},[8]={["y"]=325,["x"]=695},[9]={["y"]=415,["x"]=515},[10]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=392,["x"]=304},[2]={["y"]=240,["x"]=295},[3]={["y"]=180,["x"]=415},[4]={["y"]=270,["x"]=235},[5]={["y"]=150,["x"]=475},[6]={["y"]=250,["x"]=400},[7]={["y"]=280,["x"]=340},[8]={["y"]=220,["x"]=460},[9]={["y"]=310,["x"]=280},[10]={["y"]=190,["x"]=520}}}
	self.阵型位置.自在天魔阵法={我方={[1]={["y"]=464,["x"]=758},[2]={["y"]=445,["x"]=645},[3]={["y"]=385,["x"]=765},[4]={["y"]=475,["x"]=585},[5]={["y"]=355,["x"]=825},[6]={["y"]=355,["x"]=635},[7]={["y"]=385,["x"]=575},[8]={["y"]=325,["x"]=695},[9]={["y"]=415,["x"]=515},[10]={["y"]=295,["x"]=755}},
	敌方={[1]={["y"]=188,["x"]=347},[3]={["x"]=322-60,["y"]=432-130},[2]={["x"]=238-60,["y"]=416-130},[4]={["x"]=396-60,["y"]=424-130},[5]={["x"]=469-60,["y"]=405-130},[6]={["x"]=519-60,["y"]=369-130},[7]={["x"]=566-60,["y"]=316-130},[8]={["x"]=575-60,["y"]=267-130},[9]={["x"]=255-60,["y"]=319-130},[10]={["x"]=392,["y"]=93}}}

    self.阵型位置.单挑={
	    我方={
			[1]={["x"]=709,["y"]=421},
			[2]={["x"]=631,["y"]=362},
			[3]={["x"]=596,["y"]=488},
			[4]={["x"]=794,["y"]=358},
			[5]={["x"]=788,["y"]=485},
			[6]={["x"]=598,["y"]=342},
			[7]={["x"]=570,["y"]=390},
			[8]={["x"]=700,["y"]=320},
			[9]={["x"]=505,["y"]=425},
			[10]={["x"]=765,["y"]=285}
		},
		敌方={
			[1]={["x"]=361,["y"]=185},
			[2]={["x"]=422,["y"]=243},
			[3]={["x"]=250,["y"]=231},
			[4]={["x"]=450,["y"]=129},
			[5]={["x"]=282,["y"]=109},
			[6]={["x"]=398,["y"]=233},
			[7]={["x"]=333,["y"]=268},
			[8]={["x"]=463,["y"]=195},
			[9]={["x"]=267,["y"]=309},
			[10]={["x"]=528,["y"]=160}
		}
	}
end
return 战斗类
