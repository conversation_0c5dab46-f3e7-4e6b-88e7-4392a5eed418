{"auto_complete": {"selected_items": [["x", "x\t 选项 "]]}, "buffers": [{"file": "main.lua", "settings": {"buffer_size": 4251, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[5, 1, "insert", {"characters": "--"}, "AgAAACMOAAAAAAAAJA4AAAAAAAAAAAAAJA4AAAAAAAAlDgAAAAAAAAAAAAA", "TAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAIw4AAAAAAAAjDgAAAAAAAAAAAAAAAPC/"], [7, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-05-19 23:41:50"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDQtMDYgMDE6MDI6MDc", "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"], [13, 1, "paste", null, "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", "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"], [15, 1, "file_header_replace", {"a": 125, "b": 145, "strings": " 2025-05-19 23:42:39"}, "AQAAAH0AAAAAAAAAkQAAAAAAAAAUAAAAIDIwMjUtMDUtMTIgMDE6MDg6NDM", "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"], [1, 1, "revert", null, "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", "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"], [2, 1, "revert", null, "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", "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"], [3, 1, "revert", null, "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", "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"], [4, 1, "revert", null, "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", "GwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAACAHAAAAAAAAIAcAAAAAAAAAAAAAAADwvw"], [1, 1, "revert", null, "EwAAAAAAAAAAAAAAAAAAAAAAAAC7FgAALS0gQEF1dGhvcjog5bCP5Lmd5ZGA5Li2IC0gUVHvvJo1MjY4NDE2Ci0tIEBEYXRlOiAgIDIwMjUtMDMtMTYgMjI6MzE6MDAKLS0gQExhc3QgTW9kaWZpZWQgYnk6ICAg5Lqk5rWBUVHnvqTvvJo4MzQyNDgxOTEKLS0gQExhc3QgTW9kaWZpZWQgdGltZTogMjAyNS0wNS0xOSAyMzo0MjozOQoKX19T5pyN5YqhID0gcmVxdWlyZSgiZ2dlc2VydmVyIikoKQpmZ2Y9IiotKiIKZmdjPSJAK0AiCm1hdGgucmFuZG9tc2VlZCh0b3N0cmluZyhvcy50aW1lKCkpOnJldmVyc2UoKTpzdWIoMSwgNykpCnJlcXVpcmUoImxmcyIpCueoi+W6j+ebruW9lT1sZnMuY3VycmVudGRpcigpLi5bW1xdXQrliJ3lp4vnm67lvZU956iL5bqP55uu5b2VClNlcnZlckRpcmVjdG9yeT3nqIvluo/nm67lvZUKZm9ybWF0ID0gc3RyaW5nLmZvcm1hdApm5Ye95pWwPXJlcXVpcmUoImZmaeWHveaVsCIpLS0yCmZmaSA9IHJlcXVpcmUoImZmaSIpCmZmaS5jZGVmW1sKY29uc3QgY2hhciogeXooY29uc3QgY2hhciAqcyk7CmNvbnN0IGNoYXIqIHFqcW0oY29uc3QgY2hhciAqYik7CnZvaWQqICAgQ3JlYXRlRmlsZUEoY29uc3QgY2hhciosaW50LGludCx2b2lkKixpbnQsaW50LHZvaWQqKTsKYm9vbCAgICBEZXZpY2VJb0NvbnRyb2wodm9pZCosaW50LHZvaWQqLGludCx2b2lkKixpbnQsdm9pZCosdm9pZCopOwpib29sICBDbG9zZUhhbmRsZSh2b2lkKik7Ci8vCmludCAgICAgIE9wZW5DbGlwYm9hcmQodm9pZCopOwp2b2lkKiAgICBHZXRDbGlwYm9hcmREYXRhKHVuc2lnbmVkKTsKaW50ICAgICAgQ2xvc2VDbGlwYm9hcmQoKTsKdm9pZCogICAgR2xvYmFsTG9jayh2b2lkKik7CmludCAgICAgIEdsb2JhbFVubG9jayh2b2lkKik7CnNpemVfdCAgIEdsb2JhbFNpemUodm9pZCopOwovLwppbnQgICBHZXRQcml2YXRlUHJvZmlsZVN0cmluZ0EoY29uc3QgY2hhciosIGNvbnN0IGNoYXIqLCBjb25zdCBjaGFyKiwgY29uc3QgY2hhciosIHVuc2lnbmVkLCBjb25zdCBjaGFyKik7CmJvb2wgIFdyaXRlUHJpdmF0ZVByb2ZpbGVTdHJpbmdBKGNvbnN0IGNoYXIqLCBjb25zdCBjaGFyKiwgY29uc3QgY2hhciosIGNvbnN0IGNoYXIqKTsKCmludCAgIE9wZW5DbGlwYm9hcmQodm9pZCopOwp2b2lkKiAgIEdldENsaXBib2FyZERhdGEodW5zaWduZWQpOwppbnQgICBDbG9zZUNsaXBib2FyZCgpOwp2b2lkKiAgIEdsb2JhbExvY2sodm9pZCopOwppbnQgICBHbG9iYWxVbmxvY2sodm9pZCopOwpzaXplX3QgIEdsb2JhbFNpemUodm9pZCopOwoKaW50ICAgRW1wdHlDbGlwYm9hcmQoKTsKdm9pZCogICBHbG9iYWxBbGxvYyh1bnNpZ25lZCwgdW5zaWduZWQpOwp2b2lkKiAgIEdsb2JhbEZyZWUodm9pZCopOwp2b2lkKiAgIGxzdHJjcHkodm9pZCosY29uc3QgY2hhciopOwp2b2lkKiAgIFNldENsaXBib2FyZERhdGEodW5zaWduZWQsdm9pZCopOwovLwp0eXBlZGVmIHN0cnVjdCB7CnVuc2lnbmVkIGxvbmcgaVsyXTsgLyogbnVtYmVyIG9mIF9iaXRzXyBoYW5kbGVkIG1vZCAyXjY0ICovCnVuc2lnbmVkIGxvbmcgYnVmWzRdOyAvKiBzY3JhdGNoIGJ1ZmZlciAqLwp1bnNpZ25lZCBjaGFyIGluWzY0XTsgLyogaW5wdXQgYnVmZmVyICovCnVuc2lnbmVkIGNoYXIgZGlnZXN0WzE2XTsgLyogYWN0dWFsIGRpZ2VzdCBhZnRlciBNRDVGaW5hbCBjYWxsICovCn0gTUQ1X0NUWDsKdm9pZCBNRDVJbml0KE1ENV9DVFggKik7CnZvaWQgTUQ1VXBkYXRlKE1ENV9DVFggKiwgY29uc3QgY2hhciAqLCB1bnNpZ25lZCBpbnQpOwp2b2lkIE1ENUZpbmFsKE1ENV9DVFggKik7Ci8vCmludCAgIE1lc3NhZ2VCb3hBKHZvaWQgKiwgY29uc3QgY2hhciosIGNvbnN0IGNoYXIqLCBpbnQpOwp2b2lkICBTbGVlcChpbnQpOwppbnQgICBfYWNjZXNzKGNvbnN0IGNoYXIqLCBpbnQpOwpdXQoKLS0g5YWo5bGA5Y+Y6YeP5Yid5aeL5YyWCi0tIOiwg+ivleaooeW8j+W8gOWFs++8muWPr+S7peaJi+WKqOiuvue9ruS4umZhbHNl5p2l5YWz6Zet6LCD6K+V5qih5byPCuacrOWcsOiwg+ivleW8gOWFsyA9IHRydWUgIC0tIOiuvue9ruS4umZhbHNl5Y+v5by65Yi25YWz6Zet6LCD6K+V5qih5byPCuiwg+ivleaooeW8jyA9IF9fZ2dlLmlzZGVidWcgYW5kIOacrOWcsOiwg+ivleW8gOWFswrpmo/mnLrluo/liJc9MAoKLS0g5Yid5aeL5YyW6ZSZ6K+v5pel5b+X57O757uf77yI5a6a5LmJ5Li6X0fnmoTlhajlsYDlj5jph4/noa7kv53miYDmnInmqKHlnZflj6/orr/pl67vvIkKLS0g5L2/55SocmF3c2V056Gu5L+d6LWL5YC85LiN5Lya6KKr5YWD6KGo5oum5oiqCi0tcmF3c2V0KF9HLCAi6ZSZ6K+v5pel5b+XIiwge30pCnJhd3NldChfRywgIumUmeivr+aVsOebriIsIDApCl9fU+acjeWKoTrovpPlh7ooIumUmeivr+aXpeW/l+ezu+e7n+WIneWni+WMluWujOaIkCIpCgpfX0PlrqLmiLfkv6Hmga8gPSB7fQpfUUpCVD0i57uP5YW45oCA5penIgpfX1PmnI3liqE6572u5qCH6aKYKF9RSkJUKQrliJ3lp4vljJblrozmr5UgPSBmYWxzZQrot7PovazmtYvor5UgPSBmYWxzZQrot7Povazml7bpl7QgPSAxCuacjeWKoeerr+WPguaVsCA9IHt9CgotLSDliqDovb3liJ3lp4vljJbohJrmnKwKcmVxdWlyZSgiU2NyaXB0L+WIneWni+WMluiEmuacrCIpCgotLSDliqDovb3ns7vnu5/mqKHlnZcK54mp5ZOB5o6J6JC95o6n5Yi2ID0gcmVxdWlyZSgiU2NyaXB0L+ezu+e7ny/nianlk4HmjonokL3mjqfliLYiKQoKLS0g5Yqg6L295YWo5bGA5pWw5o2u77yI5Ye95pWw5Zyo5Yid5aeL5YyW6ISa5pys5Lit5a6a5LmJ77yJCuWKoOi9veWFqOWxgOaVsOaNrigpCgotLSDliJ3lp4vljJbpmpznoo3ns7vnu58KbG9jYWwg6Zqc56KN6ZuG5oiQID0gcmVxdWlyZSgiU2NyaXB0L+W3peWFty/muLjmiI/pmpznoo3pm4bmiJAiKQrpmpznoo3pm4bmiJAu5Yid5aeL5YyWKCkKCi0tIOWQr+WKqOe6v+eoiwrnur/nqIsgPSByZXF1aXJlKCJTY3JpcHQv57q/56iLL+WumuaXtuWZqCIpKDEsIlNjcmlwdC/nur/nqIsv57q/56iL5b6q546vIikK57q/56iLMiA9IHJlcXVpcmUoIlNjcmlwdC/nur/nqIsv5a6a5pe25ZmoMTEiKSgxLCJTY3JpcHQv57q/56iLL+e6v+eoi+W+queOrzExIikgLS3lgYfkurrnur/nqIvlkK/liqjkuK0K57q/56iLMyA9IHJlcXVpcmUoIlNjcmlwdC/nur/nqIsv5a6a5pe25ZmoMyIpKDEsIlNjcmlwdC/nur/nqIsv57q/56iL5b6q546vMyIpLS3pgZPlhbfmm7TmlrDnur/nqIsg6KeS6Imy5pu05pawCue6v+eoizUgPSByZXF1aXJlKCJTY3JpcHQv57q/56iLL+WumuaXtuWZqDUiKSgxLCJTY3JpcHQv57q/56iLL+e6v+eoi+W+queOrzUiKS0t5Zyw5Zu+57q/56iLCue6v+eoizYgPSByZXF1aXJlKCJTY3JpcHQv57q/56iLL+WumuaXtuWZqDYiKSgxLCJTY3JpcHQv57q/56iLL+e6v+eoi+W+queOrzYiKS0t5Yu+6a2C57SiCgotLSDlkK/liqjmnI3liqHlmagKX19T5pyN5YqhOuWQr+WKqCjmnI3liqHnq6/lj4LmlbAuaXAs5pyN5Yqh56uv5Y+C5pWwLuerr+WPoykKCi0tIOacjeWKoeWZqOS6i+S7tuWkhOeQhuWHveaVsApmdW5jdGlvbiBfX1PmnI3liqE65ZCv5Yqo5oiQ5YqfKCkKCXJldHVybiAwCmVuZAoKZnVuY3Rpb24gX19T5pyN5YqhOui/nuaOpei/m+WFpShJRCxJUCxQT1JUKQoJaWYgSVAgPT0g5pyN5Yqh56uv5Y+C5pWwLmlwIHRoZW4KCQlfX1PmnI3liqE66L6T5Ye6KHN0cmluZy5mb3JtYXQoJ+e9keWFs+W3sui/nuaOpe+8miglcyk6JXM6JXMnLElELCBJUCxQT1JUKSkKCQlfX0PlrqLmiLfkv6Hmga9bSURdID0gewoJCUlQID0gSVAsCgkJ6K6k6K+BPW9zLnRpbWUoKSwKCQlQT1JUID0gUE9SVAoJCX0KCQlpZiBJUD095pyN5Yqh56uv5Y+C5pWwLmlwIHRoZW4KCQkJX19D5a6i5oi35L+h5oGvW0lEXS7nvZHlhbM9dHJ1ZQoJCQnmnI3liqHnq6/lj4LmlbAu572R5YWzaWQ9SUQKCQllbmQKCWVsc2UKCQlfX1PmnI3liqE65pat5byA6L+e5o6lKElEKQoJZW5kCmVuZAoKZnVuY3Rpb24gX19T5pyN5YqhOui/nuaOpemAgOWHuihJRCkKCWlmIF9fQ+WuouaIt+S/oeaBr1tJRF0gdGhlbgoJCWlmIF9fQ+WuouaIt+S/oeaBr1tJRF0u572R5YWzIHRoZW4KCQkJX19T5pyN5YqhOui+k+WHuihzdHJpbmcuZm9ybWF0KCfnvZHlhbPlrqLmiLfpgIDlh7ooJXMpOiVzOiVzJywgSUQsX19D5a6i5oi35L+h5oGvW0lEXS5JUCxfX0PlrqLmiLfkv6Hmga9bSURdLlBPUlQpKQoJCWVuZAoJZW5kCmVuZAoKZnVuY3Rpb24gX19T5pyN5YqhOuaVsOaNruWIsOi+vihJRCwuLi4pCglsb2NhbCBhcmcgPSB7Li4ufQoJaWYgX19D5a6i5oi35L+h5oGvW0lEXSB0aGVuCgkJaWYgX19D5a6i5oi35L+h5oGvW0lEXS7nvZHlhbMgdGhlbgoJCQlpZiBhcmdbMV0gPj0xOTA5MDkyMTkgIHRoZW4KCQkJCWlmIF9fQ+WuouaIt+S/oeaBr1tJRF0uSVAgPT3mnI3liqHnq6/lj4LmlbAuaXAgdGhlbgoJCQkJCeWwj+elnue9keWFszrmlbDmja7lpITnkIYoYXJnWzFdLGFyZ1syXSkKCQkJCWVuZAoJCQllbHNlCgkJCQnnvZHnu5zlpITnkIbnsbs65pWw5o2u5aSE55CGKGFyZ1sxXSxhcmdbMl0pCgkJCWVuZAoJCWVuZAoJZW5kCmVuZAoKZnVuY3Rpb24gX19T5pyN5YqhOumUmeivr+S6i+S7tihJRCxFTyxJRSkKCWlmIF9fQ+WuouaIt+S/oeaBr1tJRF0gdGhlbgoJCV9fU+acjeWKoTrovpPlh7ooc3RyaW5nLmZvcm1hdCgn6ZSZ6K+v5LqL5Lu2KCVzKTolcywlczolcycsIElELF9f6ZSZ6K+vW0VPXSBvciBFTyxfX0PlrqLmiLfkv6Hmga9bSURdLklQLF9fQ+WuouaIt+S/oeaBr1tJRF0uUE9SVCkpCgllbmQKZW5kCgotLSDlkK/liqjmuLjmiI/mtLvliqgKZnVuY3Rpb24g5ZCv5Yqo5rS75YqoKCkKCS0tIOWcqOWIneWni+WMluiEmuacrOS4reW3sue7j+iuvue9riBRX+S4reeni+W8gOWFsyA9IHRydWXvvIzov5nph4zkuI3pnIDopoHph43lpI3orr7nva4KCeS4reeni+S7u+WKoTrlvIDlkK/kuK3np4vku7vliqEoKQoJLS3ku7vliqHlpITnkIbnsbs65bm05YW95rS75YqoKCktLeW5tOWFvQoJ5Lu75Yqh5aSE55CG57G7OuWuneWbvuWwgeWmlihpZCktLeWwgeWmlgoJ5Lu75Yqh5aSE55CG57G7Ouiuvue9ruWkp+mbgeWhlOaAqigpCgnku7vliqHlpITnkIbnsbs65aSP5pel5rOh5rOhKCkKCeS7u+WKoeWkhOeQhuexuzrosIPnmq7nmoTms6Hms6EoaWQpCgplbmQKCi0tIOiwg+ivleaooeW8j+aOp+WItuWHveaVsApmdW5jdGlvbiDliIfmjaLosIPor5XmqKHlvI8o5paw54q25oCBKQogICAgaWYg5paw54q25oCBIH49IG5pbCB0aGVuCiAgICAgICAgLS0g6K6+572u5oyH5a6a54q25oCBCiAgICAgICAgaWYg5paw54q25oCBIHRoZW4KICAgICAgICAgICAg6LCD6K+V5qih5byPID0gX19nZ2UuaXNkZWJ1ZyBhbmQg5pys5Zyw6LCD6K+V5byA5YWzCiAgICAgICAgZWxzZQogICAgICAgICAgICDosIPor5XmqKHlvI8gPSBmYWxzZQogICAgICAgIGVuZAogICAgZWxzZQogICAgICAgIC0tIOWIh+aNouW9k+WJjeeKtuaAgQogICAgICAgIOiwg+ivleaooeW8jyA9IG5vdCDosIPor5XmqKHlvI8KICAgIGVuZAoKICAgIGxvY2FsIOeKtuaAgeaWh+acrCA9IOiwg+ivleaooeW8jyBhbmQgIuW8gOWQryIgb3IgIuWFs+mXrSIKICAgIF9fU+acjeWKoTrovpPlh7ooIuiwg+ivleaooeW8j+W3siIgLi4g54q25oCB5paH5pysKQogICAgcmV0dXJuIOiwg+ivleaooeW8jwplbmQKCi0tIOiOt+WPluiwg+ivleaooeW8j+eKtuaAgQpmdW5jdGlvbiDojrflj5bosIPor5XmqKHlvI/nirbmgIEoKQogICAgcmV0dXJuIHsKICAgICAgICDlvJXmk47osIPor5UgPSBfX2dnZS5pc2RlYnVnLAogICAgICAgIOacrOWcsOW8gOWFsyA9IOacrOWcsOiwg+ivleW8gOWFsywKICAgICAgICDlvZPliY3nirbmgIEgPSDosIPor5XmqKHlvI8KICAgIH0KZW5kCgotLSDmiafooYzmtLvliqjlkK/liqgK5ZCv5Yqo5rS75YqoKCkAAAAAAAAAAJsQAAAAAAAAAAAAAAAAAAAAAAAAmxAAAAAAAAAAAAAAAAAAAAAAAACbEAAAAAAAAAAAAAAAAAAAAAAAAJsQAAAAAAAAAAAAAAAAAAAAAAAAmxAAAAAAAAAAAAAAAAAAAAAAAACbEAAAAAAAAAAAAAAAAAAAAAAAAJsQAAAAAAAAAAAAAAAAAAAAAAAAmxAAAAAAAAAAAAAAAAAAAAAAAACbEAAAAAAAAAAAAAAAAAAAAAAAAJsQAAAAAAAAAAAAAAAAAAAAAAAAmxAAAAAAAAAAAAAAAAAAAAAAAACbEAAAAAAAAAAAAAAAAAAAAAAAAJsQAAAAAAAAAAAAAAAAAAAAAAAAmxAAAAAAAAAAAAAAAAAAAAAAAACbEAAAAAAAAAAAAAAAAAAAAAAAAJsQAAAAAAAAAAAAAAAAAAAAAAAAmxAAAAAAAAAAAAAAAAAAAAAAAACbEAAAAAAAAAAAAAA", "EgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAgQAAAAAAAACBAAAAAAAAAAAAAAAADwvw"]]}], "build_system": "Packages/Lua/ggeserver.sublime-build", "build_system_choices": [[[["Packages/Lua/ggebc.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggeobj.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "Stop"]], ["Packages/Lua/ggeserver.sublime-build", "Stop"]], [[["Packages/Lua/ggeserver.sublime-build", ""], ["Packages/Lua/ggeserver.sublime-build", "Run"], ["Packages/Lua/ggeserver.sublime-build", "RunInCommand"], ["Packages/Lua/ggeserver.sublime-build", "SetGGE"], ["Packages/Lua/ggeserver.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "AboutGGE"]], ["Packages/Lua/ggeserver.sublime-build", ""]]], "build_varint": "", "command_palette": {"height": 392.0, "last_filter": "", "selected_items": [["i", "Package Control: Install Package"], ["lsp", "Package Control: List Packages"], ["Package Control: ", "Package Control: Add Channel"], ["install package", "Package Control: Install Package"], ["IN", "Package Control: Install Package"], ["in", "Package Control: Install Package"], ["install pack", "Package Control: Install Package"]], "width": 464.0}, "console": {"height": 102.0, "history": ["import urllib.request, os, hashlib;", "import urllib.request, os, hashlib; "]}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/myfwd/服务端", "/D/myfwd/服务端/<PERSON><PERSON>t", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/角色处理类", "/D/BaiduNetdiskDownload/飞蛾静脉/gge"], "file_history": ["/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派迷宫.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/角色处理类.lua", "/D/myfwd/服务端/Script/任务_小九/中秋任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/6_无名鬼城.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1130.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1123.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/7_八戒悟空.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/3_玄奘的身世.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/星辰挑战.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/队伍处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/游泳活动.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/创建战斗.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/押镖任务.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商城处理类.lua", "/D/myfwd/服务端/Script/数据中心/物品数据.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1001.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派白虎.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/属性控制/队伍.lua", "/D/myfwd/服务端/Script/初始化脚本.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/成就处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/线程/定时器3.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/五更寒.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/地图处理类/地图坐标类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局循环类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1044.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/道具处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/临时任务/首席争霸.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/技能数据库.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派PK.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派青龙.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/装备处理类.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/假人玩家.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/摆摊假人数据.lua", "/D/myfwd/服务端/Script/任务_小九/古董商人.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/剧情处理器/剧情对话调用.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗处理.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/捉鬼任务.lua", "/D/myfwd/服务端/Script/任务_小九/星官.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_封妖.lua", "/D/myfwd/服务端/Script/任务_小九/王婆西瓜.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/乌鸡国.lua", "/D/mymh/服务端/Script/系统处理类/ScriptInit.lua", "/C/Users/<USER>/Desktop/修复备份/新建文件夹/服务端/Script/系统处理类/聊天处理类.lua", "/D/mymh/服务端/<PERSON>ript/初始化脚本.lua", "/D/mymh/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/系统处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/玩家操作类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/帮派商业对话.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/网络处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/商业对话.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/助战处理类/MateControl.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商店处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/NPC对话处理.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/彩虹争霸.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/降妖伏魔.lua", "/C/Users/<USER>/Desktop/修复备份/开区前版本/服务端/main.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗技能.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗固伤计算.lua", "/D/myfwd/服务端/Script/数据中心/场景NPC.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1226.lua", "/D/myfwd/服务端/Script/数据中心/传送位置.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/传送圈位置.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/召唤兽处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/初始.lua", "/D/myfwd/服务端/Script/数据中心/宝宝.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/其他任务/大雁塔怪.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局函数.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1514.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/9_渡劫剧情.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1204.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/游戏活动类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/任务处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/藏宝阁处理类.lua", "/D/myfwd/服务端/<PERSON>ript/系统/热更新系统.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/幻域迷宫.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/锻刀村之战.lua", "/D/myfwd/服务端/Script/数据中心/明暗雷怪.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1103.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1070.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1028.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/人物修任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宠物修任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/调皮的泡泡.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/东海湾小动物.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/古董商人任务.lua", "/D/myfwd/服务端/Script/数据中心/野怪.lua", "/D/myfwd/服务端/<PERSON>ript/工具/新区重置工具.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/工具/全局数据初始化工具.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/工具/数据文件修复工具.lua", "/C/Users/<USER>/Desktop/修复备份/6.5/客户端/<PERSON>ript/初系统/无边框启动器.lua", "/C/Users/<USER>/Desktop/修复备份/6.5/服务端/<PERSON>ript/系统/热更新系统.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/线程/定时器11.lua", "/D/myfwd/服务端/Script/任务_小九/天降辰星.lua", "/D/myfwd/服务端/<PERSON><PERSON><PERSON>/对话处理类/对话调用/1815.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/神兵异兽榜.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/AI战斗.lua", "/D/myfwd/服务端/Script/任务_小九/副本任务/三生石.lua", "/D/myfwd/服务端/script/任务_小九/副本任务/三生石", "/D/myfwd/服务端/<PERSON>ript/战斗处理类/天罡星AI.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统/物品掉落控制.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1865.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派玄武.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/玩家数据类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_妖王.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/长安保卫战.lua", "/D/myfwd/服务端/Script/任务_小九/副本任务/一斛珠.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/NPC对话内容.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/皇宫飞贼.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/车迟斗法.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/镖王活动.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/通天河.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/梦魇夜叉.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/门派闯关.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/雁塔地宫.lua", "/D/myfwd/服务端/Script/数据中心/取师门.lua", "/D/myfwd/服务端/Script/任务_小九/二八星宿.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/师门任务.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/宝图_三界悬赏.lua", "/D/myfwd/服务端/Script/任务_小九/天罡星.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/泾河龙王.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/地煞星.lua", "/D/myfwd/服务端/Script/数据中心/染色.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/轮回境副本.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/帮派处理.lua", "/D/myfwd/服务端/<PERSON>ript/系统处理类/小神网关.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/翰墨丹青.lua"], "find": {"height": 43.0}, "find_in_files": {"height": 127.0, "where_history": ["D:\\myfwd\\服务端\\Script\\任务_小九,<project filters>", "D:\\myfwd\\服务端,<project filters>", "D:\\myfwd\\服务端\\Script,<project filters>", "D:\\myfwd\\服务端", "D:\\myfwd\\Server\\Script,<project filters>", "D:\\myfwd\\Server,<project filters>", "D:\\myfwd\\Server\\Script,<project filters>", "D:\\myfwd\\服务端\\Script,<project filters>", "D:\\梦幻服务端\\服务端\\Script,<project filters>", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\服务端\\Script,<project filters>", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\客户端,C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\0.543\\服务端", "D:\\梦幻服务端\\客户端", "D:\\梦幻服务端\\服务端\\Script", "D:\\【花好月圆】-三经脉版本-助战分角色+VIP礼包+会员卡+剧情活动\\服务端\\Script\\战斗处理类", "D:\\梦幻服务端\\服务端\\Script", "D:\\梦幻服务端\\源码备份\\服务端\\Script", "D:\\梦幻服务端\\服务端", "C:\\Users\\<USER>\\Desktop\\新建文件夹 (2)\\客户端\\Script", "D:\\梦幻服务端\\服务端\\Script", "D:\\死神互通全套源码\\Script", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端", "D:\\梦幻服务端\\服务端\\Script", "for i=2,5  do", "D:\\梦幻服务端\\服务端\\Script", "D:\\花好互通\\fuwudd\\Script", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\服务端\\Script", "D:\\梦幻服务端\\服务端", "D:\\梦幻服务端\\服务端,D:\\梦幻服务端\\客户端", "D:\\梦幻服务端\\服务端", "D:\\新建文件夹 (3)\\服务端源码", "D:\\梦幻服务端\\服务端", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\数据中心", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script\\对话处理类\\对话调用", "D:\\BaiduNetdiskDownload\\2008\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九\\周末玩法", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\角色处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九\\副本任务", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九\\副本任务", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\战斗处理类", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script", "D:\\BaiduNetdiskDownload\\2023.10.11【飞蛾+笑傲网关】修复渡劫飞升，网关物品\\服务端\\Script\\任务_小九"]}, "find_state": {"case_sensitive": false, "find_history": ["帮派土地公公", "迷宫", "扣除银子", "帮派迷宫", "跑商", "中秋", "衙门守卫", "阎王大人，任务已经完成了", "你已经击败了我", "饰品颜色", "视频颜色", "这象", "1123", "千年怨鬼", "鬼将", "饰品显示", "110050", "你已经知道了", "他在森罗殿", "pr", "移动坐标刷新", "警告", "pr", "队伍解散时更新", "队伍解散时", "队伍解散", "游泳", "劫镖", "1144", "劫镖", "100035", "劫镖", "取随机数", "劫镖", "指南", "150", "玄冰", "如意宫灯", "镖", "跑镖", "门派", "跑商", "纵地", "迷宫", "成就", "成就处理", "成就", "成就处理", "保存成就", "升级比赛", "副本首通", "副本", "成就", "5135", "首席", "百鬼噬魂", "帮派", "青龙", "无级别限制", "赤明", "书", "150", "书", "假人", "古董商人", "古董", "天罡气", "迷宫", "bcsj", "无级别限制", "躲避减少", "骷髅", "捉鬼", "书铁", "藏宝图", "宝图封妖", "书铁", "猹", "随机", "法防", "调试", "绿芦羹", "检查聊天处理类", "🔥", "退出战斗", "下线", "强制", "踢出", "下", "下线", "踢", "ID特效", "特效ID", "特效id", "给予跑商道具", "聊天", "9000", "不足无法兑换", "你的商店不足", "商品帽子", "@bc", "商品", "pr", "添加ID特效1", "商品帽子", "取商品卖出价格", "商品面粉", "面粉", "网络处理类加载成功", "bc", "巫医", "检查道具", "给予任务道具", "奏折", "殷", "亲笔", "书信", "纸钱", "古董商人活动定时器开始运行", "古董", "广播", "计算固伤附加属性", "雨落寒沙", "雨落", "古董商人", "小地图", "古董商人", "古董"], "highlight": false, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["告辞", "古董货商", "虞悄悄", "", ",法防 = math.floor(等级*7*难度系数*0.8)", ",防御 = math.floor(等级*7*难度系数)", "\t,防御 = math.floor(等级*5*难度系数)\n\t,法防 = math.floor(等级*5*难度系数)", "藏宝图", "", "破碎无双", "\"高级魔兽要诀\"", "", "6100", "2008", "2001", "7500", "1625", "3000", "8020", "1005", ",穿刺等级= 穿刺等级", ",穿刺等级= 计算穿刺等级", ",穿刺等级= 300", "战斗", "速度 = math.floor(sx.属性.速度*难度系数),", "法伤 = math.floor(sx.属性.法伤*难度系数),", "法伤 = math.floor(sx.属性.速度*难度系数),", "伤害 = math.floor(sx.属性.速度*难度系数),", "伤害 = math.floor(sx.属性.法伤*难度系数),", "伤害 = math.floor(sx.属性.伤害*难度系数),", "物抗=xiulian + 3", "物抗=xiulian + 5 ", "", "伤害=等级*18", "法防=等级*9", "防御=等级*11", ",技能={\"高级感知\",\"高级反震\",\"高级防御\",\"高级反震\",\"高级魔之心\",\"高级法术波动\",\"高级敏捷\",\"高级强力\",\"高级夜战\",\"高级偷袭\",\"高级必杀\"}", ",气血=等级*等级*8", ",速度=等级*6", ",技能={\"高级感知\"}", "", "法伤结果", "临时法伤结果", "1-目标数*0.1", "计算固伤附加属性", "附加属性.灵饰伤害+附加属性.符石伤害+附加属性.拭剑石", "qz(fhz*分灵+修炼差*5+神木符)", ")", "(", "战斗数据.参战单位[编号].拭剑石伤害", "战斗数据:取灵饰属性", "战斗数据:取符石属性", "固定伤害结果", "宝石属性", "", "local 武器伤害 = math.floor(战斗数据.参战单位[编号].装备伤害)", "+ 4000", "local 名称 = \"特赦令牌\"", "高级藏宝图", "local 经验 = 等级 * 取随机数(120, 130)", "local 银子 = 等级 * 取随机数(105, 120)", "local 经验 = 等级 * 取随机数(140, 160)", "特效宝珠\n\t", "\n\t", "特效宝珠", "躲避", ",速度 = math.floor(sx.属性.速度*1.5)", "取随机数(95, 105)", "等级 * 12", "速度=等级*4", ",速度=等级*3.5", ",伤害=等级*15", "取随机数(90,120)", "取随机数(140,145)", "达到", "被封印 >= 3", "铁血", "渡劫", "奖励参数", "防御 = math.floor(等级*13)", "法防 = math.floor(等级*11)", "速度 = qz(sx.属性.速度)", "", "11", "玩家数据[v].道具", "玩家数据", "玩家数据[v].道具:给予道具", "玩家数据[v].道具:", "主线=10", "self:下一页", "self:第一页", "法伤=等级*13", "sx.属性.气血*20", "sx.属性.气血*16", "气血=等级*145", "气血=等级*125", "气血=等级*140", "sx.属性.气血*10", "sx.属性.气血*12", "法伤=等级*9", "“同气”", "“淝水之战”", "“同气”", "self:取角色选择信息(id, 123)", "发送数据(玩家数据[id].连接id, 122", "发送数据(玩家数据[id].连接id, 121)", "self:取角色选择信息(id, 120)", "降妖伏魔:完成", "self", "任务id", "任务数据[任务id]", "local 银子=等级*55+取随机数(200,400)", "local 经验=等级*取随机数(115,125)", "目标1", "指令.目标", "玩家id", "指令.参数", "指令.类型", "战斗数据", "速度=等级*4", "玩家数据[id].道具:给予道具(id,\"九转金丹\",150,nil,nil,\"专用\")", "玩家数据[id].道具:给予道具(id,\"九转金丹\",50,nil,nil,\"专用\")", "", "取门派", "马副将", "--显示血量计算", "#", "属性.伤害"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "main.lua", "selected": true, "semi_transient": false, "settings": {"buffer_size": 4251, "regions": {}, "selection": [[352, 352]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 5, 9, 20, 45, 10, 2, 231, 81, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 198.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 31.0}, "input": {"height": 44.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.exec": {"height": 185.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "output.unsaved_changes": {"height": 277.0}, "pinned_build_system": "Packages/Lua/ggeserver.sublime-build", "project": "开发服务端.sublime-project", "replace": {"height": 58.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["", "服务端\\Script\\战斗处理类\\ScriptInit.lua"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [], "width": 380.0}, "select_symbol": {"height": 231.0, "last_filter": "", "selected_items": [], "width": 574.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 282.0, "status_bar_visible": true, "template_settings": {}}