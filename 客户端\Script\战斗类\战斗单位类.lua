-- @Author: baidwwy
-- @Date:   2024-05-15 22:00:53
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-02 17:35:54
local 战斗单位类 = class()
local floor = math.floor
local format = string.format
local yxs = require("script/资源类/FSB")
local tx = 引擎.取头像
local 按钮 = require("script/系统类/按钮")
function 战斗单位类:初始化()
  self.免疫 = 引擎.场景.免疫文字
  self.反弹 = 引擎.场景.反弹文字
  self.躲避 = 引擎.场景.躲避文字
  self.无穷 = 引擎.场景.无穷文字
  self.伤害图片 = tp.战斗文字[1]
  self.回复图片 = tp.战斗文字[2]
  self.暴击图片 = tp.战斗文字[4]

end

local function 取攻击帧(模型, zl, 攻击2)
  local 攻击帧, 攻击延迟, 终结帧, 攻击抖动, 攻击抖动2 = 2, 1.25, nil, false, false
  zl = 引擎.场景:取武器子类(zl)

    if 模型 == "偃无师" then
      攻击帧 = 1
      攻击延迟 = 1.5
      if zl == "巨剑" then
        攻击帧 = -1
        攻击延迟 = 1.35
      end
    elseif 模型 == "鬼潇潇" or 模型 == "桃夭夭" or 模型 == "龙太子" or 模型 == "剑侠客" or 模型 == "真陀护法" then
      攻击帧 = 1
      攻击延迟 = 1.3
      if 模型 == "龙太子" and zl == "扇" then
        攻击抖动 = true
      elseif 模型 == "鬼潇潇" and zl == "爪刺" then
        攻击抖动 = true
      end
    elseif 模型 == "玄彩娥" or 模型 == "舞天姬" or 模型 == "进阶毗舍童子" or 模型 == "羊头怪" or 模型 == "锦毛貂精" then
      攻击帧 = -1
      攻击延迟 = 1.15
      if 模型 == "舞天姬" and zl == "环圈" then
        攻击抖动 = true
      end
    elseif 模型 == "虎头怪" or 模型 == "神天兵" or 模型 == "巨魔王" or 模型 == "杀破狼" or 模型 == "持国巡守" or 模型 == "雷鸟人" or 模型 == "金饶僧" or 模型 == "葫芦宝贝" or 模型 == "凤凰" or 模型 == "野鬼" or 模型 == "帮派妖兽" or 模型 == "修罗傀儡鬼" or 模型 == "踏云兽" or 模型 == "巴蛇" or 模型 == "黑熊" then
      攻击帧 = 1
      攻击延迟 = 1.2
      if zl ~= nil then
        if zl == "弓弩" or zl == "弓弩1" then
          攻击延迟 = 0.88
        end
      end
      if 模型 == "虎头怪" and zl == "锤" then
        攻击抖动 = true
      elseif 模型 == "神天兵" and zl == "枪矛" then
        攻击抖动 = true
      end
    elseif 模型 == "强盗" or 模型 == "山贼" or 模型 == "噬天虎" or 模型 == "鼠先锋" or 模型 == "增长巡守"or 模型 == "灵灯侍者" or 模型 == "般若天女" or 模型 == "进阶雨师"  or 模型 == "野猪精" or 模型 == "超级玉兔" or 模型 == "幽萤娃娃" or 模型 == "黑熊精" or 模型 == "蚌精"  or 模型 == "机关鸟" or 模型 == "连弩车" or 模型 == "蜃气妖"  or 模型 == "虾兵" or 模型 == "蟹将" or 模型 == "兔子怪" or 模型 == "蜘蛛精" or 模型 == "花妖" or 模型 == "狐狸精" or 模型 == "哮天犬" or 模型 == "混沌兽" or 模型 == "蝴蝶仙子" or 模型 == "狼" or 模型 == "老虎" then
    攻击帧 = 2
    攻击延迟 = 1.12
    if 模型 == "强盗" or 模型 == "噬天虎" then
      攻击抖动 = true
    end
  elseif 模型 == "机关人人形" or 模型 == "机关兽" then
    攻击帧 = 2
    攻击延迟 = 1.25
  elseif 模型 == "泡泡" then
    攻击帧 = 2
    攻击延迟 = 2.1
  elseif 模型 == "混沌兽"  then
    攻击延迟 = 1.35
  elseif 模型 == "狂豹人形"  or 模型 == "幽灵"  then
    攻击帧 = -1
    攻击延迟 = 1.4
  elseif 模型 == "海毛虫" or 模型 == "巡游天神" or 模型 == "蛤蟆精" or 模型 == "超级神鼠" or 模型 == "进阶超级神鼠" then
    攻击帧 = 0
    攻击延迟 = 1
  elseif 模型 == "吸血鬼"  then
    攻击抖动 = true
  elseif 模型 == "大力金刚"  then
   -- 攻击抖动 = true
    攻击帧 = 0
  elseif 模型 == "大海龟"or 模型 == "骷髅怪"  or 模型 == "金身罗汉" or 模型 == "修罗傀儡妖" or 模型 == "曼珠沙华" or 模型 == "幽萤娃娃" then
    攻击帧 = 1
    攻击延迟 = 1.2
  elseif 模型 == "画魂" or 模型 == "羽灵神"  then
    攻击帧 = 1
    攻击延迟 = 1.1
  elseif 模型 == "天兵"or 模型 == "巨力神猿"   then
    攻击帧 = 1
    攻击延迟 = 1.25
  elseif 模型 == "地狱战神" or 模型 == "风伯"  or 模型 == "毗舍童子" or 模型 == "镜妖"  or 模型 == "千年蛇魅"or 模型 == "小龙女" or 模型 == "如意仙子" then
    攻击帧 = 0
    攻击延迟 = 1.25
  elseif 模型 == "芙蓉仙子"  then
    攻击帧 = -8
    攻击延迟 = 0
  elseif 模型 == "百足将军"  or 模型 == "天将" or 模型 == "小龙女" or 模型 == "碧水夜叉" or 模型 == "马面" or 模型 == "灵鹤" then
    攻击帧 = 3
    攻击延迟 = 1.23
  elseif 模型 == "鬼将" or 模型 == "超级神狗" or 模型 == "进阶超级神狗"then
      攻击帧 = 0
      if 暴击==true then
          攻击帧 = 4
      end
  elseif 模型 == "赌徒" then
    攻击帧 = 4
    攻击延迟 = 1.1
  elseif 模型 == "牛妖"  then
    攻击帧 = 3
    攻击延迟 = 1.26
  elseif 模型 == "古代瑞兽"  then
    攻击帧 = 4
    攻击延迟 = 1.2
  elseif 模型 == "知了王" then
    攻击帧 = 6
    攻击延迟 = 1.32
  elseif 模型 == "黑山老妖" then
    攻击帧 = 6
    攻击延迟 = 1.2
  elseif 模型 == "炎魔神" then
    攻击帧 = 3
    攻击延迟 = 1.2
  elseif 模型 == "长眉灵猴"  then
    攻击帧 = -1
    攻击延迟 = 1.23
  elseif 模型 == "骨精灵" or 模型 == "狐美人" or 模型 == "剑侠客" or 模型 == "逍遥生" or 模型 == "巫蛮儿" or 模型 == "英女侠" or 模型 == "飞燕女" then
    if zl ~= nil then
      if zl == "魔棒" then
        攻击帧 = -1
        if 模型 == "骨精灵" then
          攻击帧 = 2
          攻击抖动 = true
        end
      elseif zl == "宝珠" then
        攻击帧 = 2
      elseif 模型 == "英女侠" then
        攻击帧 = 0
      elseif 模型 == "飞燕女" and zl == "双短剑"  then
        攻击帧 = 0
      elseif 模型 == "飞燕女" and zl == "环圈"  then
        攻击帧 = 0
      elseif 模型 == "逍遥生" and (zl == "扇")  then
        攻击帧 = 0
      elseif 模型 == "逍遥生" and (zl == "剑")  then
        攻击帧 = 2
        终结帧 = 1
      elseif 模型 == "巫蛮儿" and (zl == "法杖")  then
        攻击帧 =0
      elseif 模型 == "狐美人" and zl == "爪刺" then
        攻击帧 = 0
      elseif 模型 == "狐美人" and zl == "鞭" then
        攻击帧 = 0
      end
    end
  elseif 模型 == "超级泡泡" or 模型 == "进阶超级泡泡" then
      攻击帧 = 5
      if 暴击 == true then
        攻击帧 = 7
      end
  else
      攻击帧 = 1
      攻击延迟 = 1.25
  end


  return 攻击帧, 攻击延迟, 终结帧, 攻击抖动, 攻击抖动2
end

function 战斗单位类:创建单位(数据, 队伍id, 编号)
  self.编号 = 编号
  self.数据 = 数据
  local wz = require("gge文字类")
  self.特技文字 = wz.创建(nil, 16, false, true, false)
  self.特技文字:置描边颜色(白色)
  self.特技文字:置颜色(0xFF871F78)
  wz = nil
  self.动画 = 战斗动画类()
  self.动画:创建动画(数据.模型, 数据.类型, 数据.染色方案, 数据.染色组, 数据.变异, 数据.武器, 数据.变身数据, 数据.显示饰品, 数据.饰品颜色, 数据.炫彩, 数据.炫彩组, 数据.历劫)
  self.动作 = "待战"
  self.显示xy = { x = 100, y = 150 }
  self.名称 = 数据.名称
  self.名称宽度 = floor(引擎.场景.字体表.人物字体:取宽度(数据.名称) / 2)
  self.刷新 = { x = 0, y = 0 }
  self.当前 = { x = 0, y = 0 }
  self.附加 = { x = 0, y = 0 }
  self.单位id = 数据.id
  self.单位类型 = 数据.类型
  self.主动技能 = 数据.主动技能
  self.特技技能 = 数据.特技技能
  self.追加法术 = 数据.披坚执锐
  self.如意神通 = 数据.如意神通
  self.战意 = 数据.战意
  self.主人序号 = 数据.主人序号
  self.超级战意 = 数据.超级战意
  self.五行珠 = 数据.五行珠
  self.人参果 = 数据.人参果
  self.骤雨 = 数据.骤雨
  self.气血 = 数据.气血
  self.队伍 = 数据.队伍
  self.共生 = 数据.共生
  self.法宝 = 数据.法宝
  self.不可操作 = 数据.不可操作
  self.最大气血 = 数据.最大气血
  self.气血上限 = 数据.气血上限
  self.模型 = 数据.模型
  self.移动坐标 = {}
  self.移动上限 = 30
  self.单位消失 = false
  self.移动频率 = 18
  self.攻击特效 = {}
  self.法术特效 = {}
  self.状态特效 = {}
  self.掉血开关 = false
  self.伤害序列 = {}
  self.捕捉开关 = false
  self.是否显示 = true
  self.保护 = false
  self.友伤 = false
  self.护盾 = 0
  self.护盾开关 = false
  self.武器宽度 = 0
  self.排序位置 = 数据.位置
  self.招式特效 = 数据.招式特效
  if self.单位类型 == "角色" and self.单位id == 引擎.场景.队伍[1].数字id then
    引擎.场景.队伍[1].愤怒 = 数据.愤怒
  end
  self.高度 = self.动画.动画["待战"].高度
  self.宽度 = self.动画.动画["待战"].宽度
  if self.高度 == nil then
    self.高度 = 90
  end
  if self.高度 > 100 then
    self.高度 = 100
  elseif self.高度 < 90 then
    self.高度 = 90
  end

  if 数据.武器 ~= nil and 数据.变身数据 == nil then
    self.武器子类 = 数据.武器.子类
    if 数据.武器.级别限制 < 90 then
      武器等级 = 1
    elseif 数据.武器.级别限制 <= 150 then
      武器等级 = 2
    else
      武器等级 = 3
    end
    self.武器宽度 = 武器宽度修正(self.模型, self.武器子类, 武器等级)
    self.攻击宽度, self.攻击高度 = 攻击宽度修正(self.模型)
  else
    self.攻击宽度, self.攻击高度 = 攻击宽度修正(self.模型)
  end

  self.攻击帧, self.攻击延迟, self.终结帧, self.攻击抖动, self.攻击抖动2 = 取攻击帧(self.模型, self.武器子类)

  local 位置
  if 数据.队伍 == 战斗类.队伍id then
    位置 = 战斗类.我方位置
    self.初始方向 = 2
    self.转圈方向 = 2
    self.敌我 = 1
    self.逃跑方向 = 0
  else
    位置 = 战斗类.敌方位置
    self.初始方向 = 0
    self.转圈方向 = 0
    self.敌我 = 2
    self.逃跑方向 = 2
  end

  -- 针对单挑模式的特殊处理
  if 战斗类.单挑模式 then
    -- 在单挑模式下，确认玩家身份更准确
    if 数据.id == 引擎.场景.队伍[1].数字id or 数据.主人id == 引擎.场景.队伍[1].数字id then
      位置 = 战斗类.阵型位置.单挑.我方
      self.初始方向 = 2
      self.转圈方向 = 2
      self.敌我 = 1
      self.逃跑方向 = 0
    else
      位置 = 战斗类.阵型位置.单挑.敌方
      self.初始方向 = 0
      self.转圈方向 = 0
      self.敌我 = 2
      self.逃跑方向 = 2
    end
  elseif 数据.附加阵法 ~= "普通" then
    -- 非单挑模式下的阵法处理
    if self.敌我 == 1 then
      位置 = 战斗类.阵型位置[数据.附加阵法].我方
    else
      位置 = 战斗类.阵型位置[数据.附加阵法].敌方
    end
  end

  self.方向 = self.初始方向
  self.动画:置方向(self.方向, self.动作)
  self.显示xy = { x = 位置[数据.位置].x, y = 位置[数据.位置].y }
  self.挨打坐标 = {}

  if self.敌我 == 1 then
    local C补差X, C补差Y = 0, 0
    local R补差X, R补差Y = 0, 0
    C补差X,C补差Y = (全局游戏宽度/2-400)-165,(全局游戏高度/2-300)+15 --宠
    R补差X,R补差Y = (全局游戏宽度/2-400)-170,(全局游戏高度/2-300)+5
    if 数据.位置 <= 5 then
      self.显示xy.x, self.显示xy.y = self.显示xy.x + R补差X, self.显示xy.y + R补差Y
    else
      self.显示xy.x, self.显示xy.y = self.显示xy.x + C补差X, self.显示xy.y + C补差Y
    end
    self.躲避参数 = 8
    self.躲避坐标 = 2.5
    self.反震误差 = { x = 50, y = 10 }
    self.逃跑坐标 = 3
    self.挨打坐标.x = self.显示xy.x - 60
    self.挨打坐标.y = self.显示xy.y - 28
  else -----敌方
    C补差X,C补差Y = -75+(全局游戏宽度/2-400),12+(全局游戏高度/2-300)-5
    R补差X,R补差Y = -85+(全局游戏宽度/2-400),2+(全局游戏高度/2-300)-5
    if 数据.位置 <= 5 then
      self.显示xy.x, self.显示xy.y = self.显示xy.x + R补差X, self.显示xy.y + R补差Y
    else
      self.显示xy.x, self.显示xy.y = self.显示xy.x + C补差X , self.显示xy.y + C补差Y
    end
    self.反震误差 = { x = -50, y = -10 }
    self.躲避坐标 = -2.5
    self.躲避参数 = -8
    self.逃跑坐标 = -3
    self.挨打坐标.x = self.显示xy.x + 58
    self.挨打坐标.y = self.显示xy.y + 28
  end
  self.初始xy = {}
  self.初始xy.x, self.初始xy.y = self.显示xy.x, self.显示xy.y
  self.色相变身 = 0
  self.特技内容开关 = false
  self.披坚开关 = false
  self.抖动数据 = { 开关 = false, 进程 = 0, x = 0, y = 0 }
  self.法术抖动开关 = nil  -- 初始化时应该为nil，而不是true
  self.法术抖动计时 = 0
  self.法术抖动坐标 = { x = 0, y = 0 }
  -- 初始化新增的抖动控制变量
  self.抖动延时 = nil
  self.抖动持续时间 = nil
  self.最大抖动时间 = nil
  self.物理抖动开关 = false
  self.物理抖动开关2 = false
  self.逃跑开关 = false
  self.逃跑特效 = 引擎.场景:载入特效("逃跑", self:取txz("逃跑"))
  self.喊话 = require("script/显示类/喊话").创建(引擎.场景)
  self.鼠标跟随 = false
  self.飞镖开关 = false
  self.弓弩开关 = false
  self.初始化结束 = true
  self.攻击次数 = 0
  if self.气血 <= 0 then
    if 数据.死亡击飞 then
      self.死亡参数 = 1
    else
      self.死亡参数 = 2
    end
    self:死亡处理()
  end
  self.战斗提示 = { 开关 = false, 内容 = "", 停止时间 = 0 }

  -- 添加单位类型判断
  if 数据.类型 == "bb" then
    self.单位类型 = "bb"
    -- 设置宠物的主人序号
    for i, v in pairs(战斗类.战斗单位) do
      if v.单位id == 数据.主人id then
        self.主人序号 = i
        break
      end
    end
  else
    self.单位类型 = 数据.类型
  end

  -- 设置单位类型和主人序号
  if 数据.类型 == "bb" then
    self.单位类型 = "bb"
    self.主人id = 数据.主人id  -- 保存主人id
  elseif 数据.类型 == "角色" then
    self.单位类型 = "角色"
    -- 如果是角色，检查是否有参战宝宝
    if 数据.参战宝宝 then
      self.参战宝宝id = 数据.参战宝宝.id
    end
  else
    self.单位类型 = 数据.类型
  end

  -- 在创建单位完成后，设置宠物和主人的关联
  if self.单位类型 == "bb" then
    -- 遍历已有单位，找到主人
    for i, v in pairs(战斗类.战斗单位[ljcs]) do
      if v.单位类型 == "角色" and v.参战宝宝id == self.数据.id then
        self.主人序号 = i
--        print("DEBUG: 宠物找到主人，主人序号:", i)
        break
      end
    end
  elseif self.单位类型 == "角色" and self.参战宝宝id then
    -- 遍历已有单位，找到宠物
    for i, v in pairs(战斗类.战斗单位[ljcs]) do
      if v.单位类型 == "bb" and v.数据.id == self.参战宝宝id then
        v.主人序号 = self.编号
    --    print("DEBUG: 角色找到宠物，宠物序号:", i)
        break
      end
    end
  end
end

function 战斗单位类:更改模型(模型, 类型, 染色方案, 染色组, 变异, 武器, 变身, 饰品, 饰品颜色, 炫彩, 炫彩组, 锦衣, 名称, 历劫)
  self.动画 = 战斗动画类()
  self.动画:创建动画(模型, 类型, 染色方案, 染色组, 变异, 武器, 变身数据, 显示饰品, 饰品颜色, 炫彩, 炫彩组, 历劫)
  self.模型 = 模型
  self.数据.变身数据 = 模型
  if 名称 then
    self.名称 = 名称
  end
  self.攻击帧, self.攻击延迟, self.终结帧 = 取攻击帧(self.模型, self.武器子类)
  self.高度 = self.动画.动画["待战"].信息组[0][3]
  if self.高度 > 120 then
    self.高度 = 120
  elseif self.高度 < 60 then
    self.高度 = 60
  elseif self.高度 < 85 then
    self.高度 = 85
  end
  if self.位置 == 2 then
    self.高度 = self.高度 + 20
  else
    self.高度 = self.高度 + 20
  end
  self.动画:置方向(self.方向, self.动作)
end

function 战斗单位类:设置鼠标跟随(特效)
  if 1 == 1 then return end
  if self.鼠标跟随 then
    self.鼠标跟随 = false
  else
    self.鼠标跟随 = true
  end
end

function 战斗单位类:取移动坐标(类型, 攻击编号)
  if 类型 == "挨打" then
    return self.挨打坐标.x , self.挨打坐标.y
  elseif 类型 == "友伤" then
    return self.显示xy.x,self.显示xy.y
  elseif 类型 == "保护" then
    return self.显示xy.x,self.显示xy.y
  elseif 类型 == "返回" then
    return self.初始xy.x,self.初始xy.y
  end
end

function 战斗单位类:取txz(特效)
  if Fighttxz[特效] then
    return Fighttxz[特效]
  end
  if 特效 == "宠物_静" then
    local mt = ptmx(引擎.场景.宠物.模型)
    return 引擎.场景.资源:载入(mt[3], "网易WDF动画", mt[1])
  elseif 特效 == "宠物_走" then
    local mt = ptmx(引擎.场景.宠物.模型)
    return 引擎.场景.资源:载入(mt[3], "网易WDF动画", mt[2])
  end
  return 1.15
end

function 战斗单位类:状态前置(特效)
  if 特效 ~= nil then
    local qianzhi = true
    local py = { 0, 0 }
    if 特效 == "苍白纸人" and self.敌我 ~= 1 then
      py = { -6, 0 }
      qianzhi = true
      return { cp = qianzhi, py = py }
    elseif 特效 == "紧箍咒" or 特效 == "爪印" then
      py = { 2, -self.高度 / 2 - 4 }
      return { cp = qianzhi, py = py }
    end
    if Fightztqz[特效] then
      return { cp = Fightztqz[特效].cp, py = Fightztqz[特效].py }
    end
    return { cp = qianzhi, py = py }
  end
end

function 战斗单位类:增加状态(名称, 回合, 层数, 颜色, 负面状态)
  if 名称 ~= nil and 引擎.场景 ~= nil then
    self.状态特效[名称] = self:状态前置(名称)
    local 名称1 = 名称
    if Fightdiwo[名称1] then
      if self.敌我 == 1 then
        名称1 = 名称1 .. "_我方"
      else
        名称1 = 名称1 .. "_敌方"
      end
    end
    local qtb = 引擎.取技能(名称)
    if qtb[1] ~= nil and qtb[6] ~= nil and qtb[7] ~= nil then
      self.状态特效[名称].小图标 = 引擎.场景.资源:载入(qtb[6], "网易WDF动画", qtb[8])
    else
      self.状态特效[名称].小图标 = 引擎.场景.资源:载入('wzife.wdf', "网易WDF动画", 2844166683)
    end
    self.状态特效[名称].回合 = 回合
    if 层数 then
      self.状态特效[名称].层数 = 层数
    end
    if 颜色 then
      self.状态特效[名称].颜色 = 颜色
    end
    if 负面状态 then
      self.状态特效[名称].负面状态 = 负面状态
    end
    if not self:透明状态("状态_" .. 名称1) and not self:无需状态(名称1) then
      self.状态特效[名称].动画 = 引擎.场景:载入特效("状态_" .. 名称1, self:取txz(名称))
      if 名称1 == "盾气_我方" then
        self.状态特效[名称].动画:置翻转(true)
      end
    end
    if 名称 == "护盾" and self.状态特效["铜头铁臂"] ~= nil then
      if self.状态特效["铜头铁臂"].颜色 == "青" then
        self.状态特效["护盾"].动画:置颜色(0xFF00FFFF)
      elseif self.状态特效["铜头铁臂"].颜色 == "红" then
        self.状态特效["护盾"].动画:置颜色(0xFFFF1111)
      end
    elseif 名称 == "炼魂" then
      self.状态特效["炼魂"].动画:置高亮模式(0xFF000FFF)
    -- elseif 名称=="雷怒霆激" then
    --   self.分身类型=1
    --   self.分身显示xy={}
    --   self.分身显示xy.x,self.分身显示xy.y=self.显示xy.x,self.显示xy.y
    --      self.分身类型1=1
    --   self.分身显示xy1={}
    --   self.分身显示xy1.x,self.分身显示xy1.y=self.显示xy.x,self.显示xy.y
    --   self.分身计时 = os.time()
    --   self.分身开关=true
    end
  end
end

function 战斗单位类:释放()
  self.动画:释放()
  if #self.攻击特效 > 0 then
    for i = 1, #self.攻击特效 do
      self.攻击特效[i]:释放()
    end
  end
  if #self.法术特效 > 0 then
    for i = 1, #self.法术特效 do
      self.法术特效[i]:释放()
    end
  end
  if #self.状态特效 > 0 then
    for i = 1, #self.状态特效 do
      self.状态特效[i].动画:释放()
    end
  end
  self.主动技能=nil
  self.特技技能=nil
  self.追加法术=nil
  self.如意神通=nil
  self.战意=nil
  self.主人序号=nil
  self.超级战意=nil
  self.五行珠=nil
  self.人参果=nil
  self.骤雨=nil
  self.气血=nil
  self.队伍=nil
  self.共生=nil
  self.法宝=nil
  self.不可操作=nil
  self.最大气血=nil
  self.气血上限=nil
  self.模型=nil
  self.攻击特效={}
  self.法术特效={}
  self.状态特效={}
  self.伤害序列 = {}
end

function 战斗单位类:添加攻击特效(名称, 加速)
  -- 添加前先清空现有的攻击特效，避免特效叠加
  if #self.攻击特效 > 0 then
    for i = 1, #self.攻击特效 do
      if self.攻击特效[i] and type(self.攻击特效[i]) == "table" and self.攻击特效[i].释放 then
        self.攻击特效[i]:释放()
      end
    end
    self.攻击特效 = {}
  end

  local 名称1 = 名称
  if not self:无需物理特效(名称) then
    local txz = self:取txz(名称)
    if 战斗类.战斗单位[ljcs][战斗类.战斗流程[1].攻击方] ~= nil and 战斗类.战斗单位[ljcs][战斗类.战斗流程[1].攻击方].招式特效 ~= nil and 战斗类.战斗单位[ljcs][战斗类.战斗流程[1].攻击方].招式特效[名称] ~= nil and 战斗类.战斗单位[ljcs][战斗类.战斗流程[1].攻击方].招式特效[名称] == true then
      local lssj = 引擎.特效库("新_" .. 名称)
      if lssj[1] ~= nil and lssj[2] ~= nil then
        名称1 = "新_" .. 名称1
      end
    end
    self.攻击特效[#self.攻击特效 + 1] = 引擎.场景:载入特效(名称1, txz)
    self.攻击特效[#self.攻击特效]:置提速(txz)
    self.攻击特效[#self.攻击特效].当前帧 = 0
    if Fight特效加速[名称1] then
      if Fight特效加速[名称1].加速 then
        self.攻击特效[#self.攻击特效].加速 = Fight特效加速[名称1].加速
      end
    end
  end
end

function 战斗单位类:添加法术特效(名称, 加速)
  -- 添加前先清空现有的法术特效，避免特效叠加
  if #self.法术特效 > 0 then
    for i = 1, #self.法术特效 do
      if self.法术特效[i] and type(self.法术特效[i]) == "table" and self.法术特效[i].释放 then
        self.法术特效[i]:释放()
      end
    end
    self.法术特效 = {}
  end

  local 名称1 = 名称
  if 名称1 == "干将莫邪" or 名称1 == "混元伞" or 名称1 == "断穹巨剑"
      or 名称1 == "风舞心经" or 名称1 == "赤焰" or 名称1 == "鸿渐于陆" or 名称1 == "铜头铁臂" then
    if self.敌我 == 1 then
      名称1 = 名称1 .. "_我方"
    else
      名称1 = 名称1 .. "_敌方"
    end
  end
  if 名称 == "鲲鹏出场" then
    战斗类.背景状态 = 1
  end
  if 名称 == "九尾出场" then
    战斗类.背景状态 = 3
  end
  if not self:无需法术特效(名称) then
    local txz = self:取txz(名称)
    if 战斗类.战斗单位[ljcs][战斗类.战斗流程[1].攻击方] ~= nil and 战斗类.战斗单位[ljcs][战斗类.战斗流程[1].攻击方].招式特效 ~= nil and 战斗类.战斗单位[ljcs][战斗类.战斗流程[1].攻击方].招式特效[名称] ~= nil and 战斗类.战斗单位[ljcs][战斗类.战斗流程[1].攻击方].招式特效[名称] == true then
      local lssj = 引擎.特效库("新_" .. 名称)
      if lssj[1] ~= nil and lssj[2] ~= nil then
        名称1 = "新_" .. 名称1
        txz = 1.5
      end
    end
    self.法术特效[#self.法术特效 + 1] = 引擎.场景:载入特效(名称1, txz)
    self.法术特效[#self.法术特效].附加y = 0
    self.法术特效[#self.法术特效].附加x = 0
    -- 优化特效更新频率
    -- if 名称 == "龙卷雨击" and  not 名称1 then
    --   self.法术特效[#self.法术特效].更新频率 = 2
    -- end
    if Fight特效加速[名称1] then
      self.法术特效[#self.法术特效].附加y = Fight特效加速[名称1].附加y or 0
      if Fight特效加速[名称1].加速 then
        self.法术特效[#self.法术特效].加速 = Fight特效加速[名称1].加速
      end
    end
  else
    战斗类.掉血流程 = nil
  end
  if 名称 == "鲲鹏出场" then
    self.法术特效[#self.法术特效].附加y = -60
    self.法术特效[#self.法术特效].附加x = -180
  elseif 名称 == "九尾出场" then
    self.法术特效[#self.法术特效].附加y = -70
    self.法术特效[#self.法术特效].附加x = -110
  elseif 名称 == "阳炎" then
    self.法术特效[#self.法术特效].附加y = -200
    self.法术特效[#self.法术特效].附加x = -130
  elseif 名称 == "琴音三叠1" then
    self.法术特效[#self.法术特效].附加y = -40
  elseif 名称 == "琴音三叠2" then
    self.法术特效[#self.法术特效].附加y = -20
  elseif 名称 == "琴音三叠3" then
    self.法术特效[#self.法术特效].附加y = -10
  end
end

function 战斗单位类:加载特效(名称)
    local txz = self:取txz(名称)
    local 名称1 = 名称
    local cp = 引擎.场景:载入特效(名称1, txz, 不显示)
    cp:置提速(txz)
    return cp
end

function 战斗单位类:换方向(方向)
  self.动画:置方向(方向, self.动作)
end

function 战斗单位类:取当前帧()
  return self.动画:取当前帧(self.动作)
end

function 战斗单位类:取结束帧()
  return self.动画:取结束帧(self.动作)
end

function 战斗单位类:取开始帧()
  return self.动画:取开始帧(self.动作)
end

function 战斗单位类:取间隔()
  return self.动画.动画[self.动作]:取间隔()
end

function 战斗单位类:取中间()
  return self.动画.动画[self.动作]:取中间()
end

function 战斗单位类:无需状态(效果)
  if skill无需状态[效果] then
    return true
  end
  return false
end

function 战斗单位类:透明状态(效果)
  if 效果 == "状态_金刚护法" or 效果 == "状态_逆鳞"  or 效果 == "状态_兵解符" then
    return true
  end
  return false
end

function 战斗单位类:无需物理特效(效果)
  if skill无需物理[效果] then
    return true
  end
  return false
end

function 战斗单位类:无需法术特效(效果)
  if skill无需法术[效果] then
    return true
  end
  return false
end

function 战斗单位类:回合结束重置()
  -- 释放攻击特效资源
  if #self.攻击特效 > 0 then
    for i = 1, #self.攻击特效 do
      if self.攻击特效[i] and type(self.攻击特效[i]) == "table" and self.攻击特效[i].释放 then
        self.攻击特效[i]:释放()
      end
    end
  end
  self.攻击特效={}

  -- 释放法术特效资源
  if #self.法术特效 > 0 then
    for i = 1, #self.法术特效 do
      if self.法术特效[i] and type(self.法术特效[i]) == "table" and self.法术特效[i].释放 then
        self.法术特效[i]:释放()
      end
    end
  end
  self.法术特效={}

  self.攻击次数 = 0
  self.攻击方向已设置 = false

  -- 清理结尾气血延迟状态
  self.结尾掉血延迟 = nil
  self.结尾掉血数值 = nil
  self.结尾掉血 = nil

  -- 如果法术抖动开关仍然存在，打印警告
  if self.法术抖动开关 ~= nil then
    print("【抖动调试】警告！单位"..self.编号.."在回合结束时法术抖动开关仍然存在："..tostring(self.法术抖动开关))
    -- 强制清理抖动状态
    self.法术抖动开关 = nil
    self.法术抖动坐标 = { x = 0, y = 0 }
    self.法术抖动计时 = 0
    self.抖动延时 = nil
    self.抖动持续时间 = nil
    self.最大抖动时间 = nil
    print("【抖动调试】已强制清理单位"..self.编号.."的抖动状态")
  end

  -- 如果单位已死亡，不重置方向
  if not self.死亡开关 then
    if self.初始方向 ~= self.方向 then
      self.方向 = self.初始方向
      self.动画:置方向(self.初始方向, self.动作)
    end
  end
end

function 战斗单位类:施法前掉血(结尾气血)
  if self.结尾掉血 == nil then
    self.结尾掉血 = 结尾气血
  end
end

function 战斗单位类:换动作(动作, 复原, 更新, 结尾气血, 挨打方死亡)
  if 动作 == "攻击" then
    self.攻击次数 = self.攻击次数 + 1 --这些都要改的
    if self.攻击次数 == 2 then
      动作 = "攻击2"
    end
    self.动画:置帧率(动作,0.1)
  end
  self.动作=动作
  self.动画:置方向(self.偷袭方向 or self.方向,self.动作)
  self.动作复原=复原
  self.停止更新=更新
  if self.结尾掉血==nil then
    self.结尾掉血=结尾气血
  end
  local 临时模型=self.数据.模型
  if self.数据.类型=="角色" or self.数据.类型=="系统角色" then
    if self.数据.武器~=nil and 引擎.场景~=nil then
      if self.数据.武器.名称 == "龙鸣寒水" or self.数据.武器.名称 == "非攻" then
        local wq = "弓弩1"
        临时模型=临时模型.."_"..wq
      else
        local wq = 引擎.场景:取武器子类(self.数据.武器.子类)
        临时模型=临时模型.."_"..wq
      end
    end
  end
  if self.数据.变身数据~=nil then
    临时模型=self.数据.变身数据
  end
  -- 首先确保临时音乐不为空
  local 临时音乐 = 引擎.取音效(临时模型)
  if 临时音乐 ~= nil then
      -- 检查是否有攻击音效，若有则赋值给攻击2
      if 临时音乐.攻击 ~= nil then
          临时音乐.攻击2 = 临时音乐.攻击
      end
      -- 检查动作对应的音效是否存在
      if 临时音乐[动作] ~= nil then
          -- 调用音效类，播放对应的动作音效
          tp:战斗音效类(临时音乐[动作], 临时音乐.资源, 动作)
      end
  end
end

function 战斗单位类:音效类无叠加(文件号, 资源包, 子类)
    -- 检查窗口是否激活
    if not 引擎.是否在窗口内() then
        return
    end

    if 文件号 ~= nil and 文件号 ~= 0 and tp ~= nil and tp.音效开启 and (not tp.战斗音效序列[文件号] or not tp.战斗音效序列[文件号]:是否播放()) then
        tp.战斗音效序列[文件号] = yxs(tp.资源:读数据(资源包, 文件号))
        tp.战斗音效序列[文件号]:播放(false):置音量(math.floor(tp.当前音效音量))
    end
end

function 战斗单位类:设置飞镖(xy, 方向, 伤害, 死亡, 名称)
  self.飞镖数据 = { x = xy.x, y = xy.y, 伤害 = 伤害, 死亡 = 死亡 }
  self.飞镖动画 = 引擎.场景:载入特效(名称, self:取txz(名称))
  self.飞镖xy = { x = xy.x, y = xy.y, 伤害 = 伤害, 死亡 = 死亡 }
  self.飞镖动画:置方向(角度算八方向(取两点角度(生成XY(xy.x, xy.y), 生成XY(self.显示xy.x, self.显示xy.y))))
  self.飞镖开关 = true
end

function 战斗单位类:设置弓弩(xy)
  self.弓弩动画 = 引擎.场景:载入特效("弓弩1", self:取txz("弓弩1"))
  self.弓弩xy = { x = xy.x, y = xy.y - 40 }
  self.弓弩动画:置方向(角度算八方向(取两点角度(生成XY(xy.x, xy.y), 生成XY(self.显示xy.x, self.显示xy.y))))
  self.弓弩开关 = true
end

function 战斗单位类:开启转圈()
  self.转圈开关 = true
end

function 战斗单位类:关闭转圈()
  self.转圈开关=false
  self.方向=self.转圈方向
  self.初始方向=self.转圈方向
  self.动画:置方向(self.初始方向,"待战")
end

function 战斗单位类:转圈处理()
  -- print(11111)
  self.初始方向=self.初始方向+0.5
  if self.初始方向>=4 then self.初始方向=0 end
  self.方向=self.初始方向
  self.动画:置方向(self.初始方向,"待战")
  -- self:换动作("待战")
end

function 战斗单位类:开启击退(死亡,x,y)
  -- 如果是木桩则不处理击退效果
  if self.模型 == "木桩" then
    self.击退开关 = false
    self.物理抖动开关 = false
    if 死亡 ~= nil then
      self.是否显示 = false
    end
    return
  end

  -- 计算斜率
  if x==nil or y ==nil or x==self.显示xy.x then
    self.斜率 = 0.1
  else
    self.斜率 = (y-self.显示xy.y)/(x-self.显示xy.x)
  end
  self.斜率=(string.format("%.2f", self.斜率))+0 --保留两位小数

  -- 初始化击退相关参数
  self.击退开关 = true
  self.击退延迟 = true
  self.法术抖动计时 = 0
  self.抖动延时 = nil
  self.法术抖动坐标 = { x = 0, y = 0 }
  self.法术抖动开关 = nil
  -- 重置新增的抖动控制变量
  self.抖动持续时间 = nil
  self.最大抖动时间 = nil
  self.物理抖动开关 = false
  self.物理抖动开关2 = false

  self.偏移类型 = 1
  self.死亡参数 = 死亡
  self.击退坐标 = {}
  self.击退坐标.x, self.击退坐标.y = self.显示xy.x, self.显示xy.y

  -- 大力金刚特殊处理
  local 攻击方 = 战斗类.战斗单位[ljcs][战斗类.战斗流程[1].攻击方]
  if 攻击方 ~= nil and 攻击方.模型 == "大力金刚" and 攻击方.法术攻击 ~= true then
    if self.死亡参数 ~= nil then
      -- 致死攻击时有更大的击退效果
      self.停止偏移 = 120  -- 致死攻击的击退距离
      self.大力金刚致死 = true  -- 标记为大力金刚致死
      if self.敌我==2 then
        self.偏移坐标=-8  -- 致死攻击的击退速度
      else
        self.偏移坐标=8
      end
    else
      -- 普通攻击的击退效果
      self.停止偏移 = 80
      if self.敌我==2 then
        self.偏移坐标=-2
      else
        self.偏移坐标=2
      end
    end
  else
    -- 其他普通攻击
    self.停止偏移 = 35
    if self.敌我==2 then
      self.偏移坐标=-1
      if self.死亡参数~=nil then
        self.偏移坐标=-0.4
      end
    else
      self.偏移坐标=1
      if self.死亡参数~=nil then
        self.偏移坐标=0.4
      end
    end
  end
end

function 战斗单位类:击退处理()
  if self.偏移类型==1 then
    self.显示xy.x,self.显示xy.y=self.显示xy.x+self.偏移坐标,self.显示xy.y+self.偏移坐标
    if(取两点距离(生成XY(self.击退坐标.x,self.击退坐标.y),生成XY(self.显示xy.x,self.显示xy.y))>self.停止偏移)then
      if self.大力金刚致死 then
        -- 大力金刚致死攻击，直接在击退位置击飞
        self.击退坐标.x, self.击退坐标.y = self.显示xy.x, self.显示xy.y
        self:死亡处理()
        self.偏移类型=0
        self.击退开关=false
        return
      end

      self.偏移类型=2
      if self.死亡参数==1 then
        self:死亡处理()
        self.显示xy.x,self.显示xy.y=self.击退坐标.x,self.击退坐标.y
        self.偏移类型=0
        self.击退开关=false
      else
        if self.敌我==2 then
          self.偏移坐标=-1
          if self.死亡参数~=0 then
            self.偏移坐标=-2.5
          end
        else
          self.偏移坐标=1
          if self.死亡参数~=0 then
            self.偏移坐标=2.5
          end
        end
        self.偏移类型=2
      end
    end
  elseif self.偏移类型==2 then
    -- 大力金刚致死攻击不执行返回动作
    if self.大力金刚致死 then
      return
    end

    self.显示xy.x,self.显示xy.y=self.显示xy.x-self.偏移坐标,self.显示xy.y-self.偏移坐标
    if(取两点距离(生成XY(self.击退坐标.x,self.击退坐标.y),生成XY(self.显示xy.x,self.显示xy.y))<=10)then
      self.显示xy.x,self.显示xy.y=self.击退坐标.x,self.击退坐标.y
      self.偏移类型=0
      self.击退开关=false

      if self.死亡参数==2 then
        self:死亡处理()
      else
        self.动作="待战"
        self:换动作(self.动作,nil,nil)
      end
    end
  end
end

function 战斗单位类:死亡处理()
 self.死亡开关=true
  if self.死亡参数==1 then
    self.撞击次数=0
    self.击飞开关=true
    self.击飞时间=引擎.取游戏时间()
    self.速度x,self.速度y=0,0
    -- 添加随机反弹次数
    self.反弹次数 = math.random(0,2) -- 随机0-2次反弹

    if not self.斜率 then
        self.斜率=0.1
    end
    -- self.动画.按帧更新=true
    if self.敌我==2 then --敌方
      if self.斜率<0.74 then --往左
        self.速度x = -16
        self.速度y = self.斜率*self.速度x
      else--往上
        self.速度y = -16
        if self.斜率==0 then
            self.斜率=6.7
        end
        self.速度x = -16/self.斜率
      end
    else
      -- print(self.斜率)
      if self.斜率<0.74 then --往右
        self.速度x = 16
        self.速度y = self.斜率*self.速度x
      else--往下
        self.速度y = 16
        if self.斜率==0 then
            self.斜率=6.7
        end
        self.速度x = 16/self.斜率
      end
    end
    local 临时音乐 = 引擎.取音效("击飞")
    if 临时音乐 ~= nil and 临时音乐.文件 ~= nil then
      self:音效类无叠加(临时音乐.文件, 临时音乐.资源, '1')
    end
  elseif self.死亡参数 == 2 then
    self.动作 = "死亡"
    local 临时模型 = self.模型
    if self.数据.变身数据 ~= nil then
      临时模型 = self.数据.变身数据
    end
    local 临时音乐 = 引擎.取音效(临时模型)
    if 临时音乐 ~= nil and 临时音乐[self.动作] ~= nil then
      tp:战斗音效类(临时音乐["死亡"], 临时音乐.资源)
    end
    self.停止更新 = false
    self:换方向(self.方向)
    self.倒地开关 = true
    self.停止更新 = true
  end
end

function 战斗单位类:击飞处理()
  self.显示xy.x, self.显示xy.y = self.显示xy.x + self.速度x, self.显示xy.y + self.速度y

  -- 旋转效果
  if 引擎.取游戏时间() - self.击飞时间 >= 0.005 then
    self.方向 = self.方向 - 0.1
    if self.方向 < 0 then self.方向 = 3 end
    self.击飞时间 = 引擎.取游戏时间()
    self:换方向(self.方向)
  end

  -- 检查是否需要反弹
  if self.反弹次数 and self.反弹次数 > 0 then
    if self.显示xy.x < 10 or self.显示xy.x > 全局游戏宽度 - 10 or
       self.显示xy.y < 10 or self.显示xy.y > 全局游戏高度 + 10 then

      -- 加速效果(每次反弹增加15%速度)
      self.速度x = self.速度x * 1.15
      self.速度y = self.速度y * 1.15

      -- 反弹
      if self.显示xy.x < 10 or self.显示xy.x > 全局游戏宽度 + 50 then
        self.速度x = -self.速度x
        -- 添加较小的随机偏移
        self.速度y = self.速度y + (math.random() - 0.5) * 4
      end
      if self.显示xy.y < 10 or self.显示xy.y > 全局游戏高度 + 50 then
        self.速度y = -self.速度y
        -- 添加较小的随机偏移
        self.速度x = self.速度x + (math.random() - 0.5) * 4
      end

      -- 限制最大速度
      local 最大速度 = 25
      if math.abs(self.速度x) > 最大速度 then
        self.速度x = 最大速度 * (self.速度x > 0 and 1 or -1)
      end
      if math.abs(self.速度y) > 最大速度 then
        self.速度y = 最大速度 * (self.速度y > 0 and 1 or -1)
      end

      self.反弹次数 = self.反弹次数 - 1

      -- 播放反弹音效
      local 临时音乐 = 引擎.取音效("击飞")
      if 临时音乐 ~= nil and 临时音乐.文件 ~= nil then
        self:音效类无叠加(临时音乐.文件, 临时音乐.资源, '1')
      end
    end
  else
    -- 检查是否飞出屏幕
    if self.显示xy.x < -50 or self.显示xy.x > 全局游戏宽度 + 50 or
       self.显示xy.y < -50 or self.显示xy.y > 全局游戏高度 + 50 then
      -- 直接移除怪物
      self.击飞开关 = false
      self.是否显示 = false
      return
    end
  end
end


function 战斗单位类:死亡处理1(死亡,x,y)
  self.死亡参数=死亡
  if x==nil or y ==nil or x==self.显示xy.x then
      self.斜率 = 0.1
  else
    self.斜率 = (y-self.显示xy.y)/(x-self.显示xy.x)
  end
  self.斜率=(string.format("%.2f", self.斜率))+0 --保留两位小数
  self:死亡处理()
end

function 战斗单位类:倒地处理()
  if self.动画:取当前帧(self.动作) == self.动画:取结束帧(self.动作) then
    self.动作 = "死亡"
    self.停止更新 = true
    self.倒地开关 = false
  end
end

function 战斗单位类:返回事件()
  --print(取两点距离(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动坐标.x,self.移动坐标.y)))
  if 取两点距离(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动坐标.x,self.移动坐标.y))>=20 then
    self.攻击次数 = 0
    self.移动距离=取移动坐标(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动坐标.x,self.移动坐标.y),27)
    -- if not self.方向开关 then
    --  self.动作="返回"
      -- self.动画:置帧率(self.动作,0.02)
      self.角度=取两点角度(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动距离.x,self.移动距离.y))
      self:换方向(角度算四方向(self.角度))
    --  self.方向开关=true
    -- end
    self.显示xy.x,self.显示xy.y=self.移动距离.x,self.移动距离.y
  else
    self.返回开关=false
    self.动作="待战"
    self:换方向(self.初始方向)
    self.显示xy.x,self.显示xy.y=self.初始xy.x,self.初始xy.y

    -- 返回完成后立即处理结尾气血掉血
    if self.结尾掉血 ~= nil and self.结尾掉血 > 0 then
        -- 直接显示掉血，不使用延迟
        self:设置掉血(self.结尾掉血, 1)
        self.结尾掉血 = nil
    end
    -- self.方向开关=false
  end
end

function 战斗单位类:移动事件()
    -- 设置默认值
    self.移动上限 = 30
    local 距离 = 0
    local 距离2 = 0

    -- 检查移动坐标是否存在
    if not self.移动坐标 then
        self.移动开关 = false
        return
    end

    -- 获取当前模型和攻击宽度/高度
    local 当前模型 = self.数据.变身数据 or self.模型
    local 当前攻击宽度, 当前攻击高度 = 攻击宽度修正(当前模型)

    -- 特殊武器处理（武器子类14）
    if self.武器子类 == 14 and not self.保护 then
        self.角度 = 取两点角度(生成XY(self.显示xy.x, self.显示xy.y), 生成XY(self.移动坐标.x, self.移动坐标.y))
        self.方向 = 角度算四方向(self.角度)
        self.动画:置方向(self.方向, self.动作)
        self.移动开关 = false
        self.友伤 = false
        return
    end

    -- 根据单位状态计算移动参数
    if self.保护 then
        -- 保护状态的移动参数
        self.移动上限 = 20
        if self.敌我 == 1 then
            距离 = -20
            距离2 = 20
        else
            距离 = 20
            距离2 = 0
        end
    -- elseif self.友伤 then
    --     -- 友伤状态的移动参数
    --     self.移动上限 = 20
    --     if self.敌我 == 1 then
    --         距离 = self.攻击宽度 + (self.武器宽度)
    --         距离2 = self.攻击高度
    --     else
    --         距离 = -self.攻击宽度 - (self.武器宽度)
    --         距离2 = -self.攻击高度
    --     end
    else
        -- 普通状态的移动参数
        if self.数据.变身数据 then
            距离 = (self.敌我 == 1) and 当前攻击宽度 or -当前攻击宽度
        else
            距离 = (self.敌我 == 1) and (当前攻击宽度 + self.武器宽度) or -(当前攻击宽度 + self.武器宽度)
        end
        距离2 = (self.敌我 == 1) and 当前攻击高度 or -当前攻击高度
    end

    -- 计算目标坐标和当前距离
    local 目标坐标 = 生成XY(self.移动坐标.x + 距离, self.移动坐标.y + 距离2)
    local 当前距离 = 取两点距离(生成XY(self.显示xy.x, self.显示xy.y), 目标坐标)

    -- 根据距离动态调整移动速度
    local 移动速度 = self.移动频率 or 12
    if 当前距离 < self.移动上限 * 0.5 then
        移动速度 = 移动速度 * 0.5
    end

    -- 根据距离决定移动方式
    if 当前距离 >= 8 then
        -- 距离较远，继续移动
        if not self.方向开关 then
            -- 首次移动时设置方向
            self.角度 = 取两点角度(生成XY(self.显示xy.x, self.显示xy.y), 目标坐标)
            self.方向开关 = true
        end

        -- 计算移动坐标并更新位置
        local 移动坐标 = 取移动坐标(生成XY(self.显示xy.x, self.显示xy.y), 目标坐标, 移动速度)
        self.显示xy.x = 移动坐标.x
        self.显示xy.y = 移动坐标.y
    else
        -- 距离较近，直接到达目标位置
        self.显示xy.x = 目标坐标.x
        self.显示xy.y = 目标坐标.y

        -- 重置移动相关状态
        self.移动开关 = false
        self.移动坐标 = {}
        self.方向开关 = false
        self.保护 = false
        self.友伤 = false

        -- 重置攻击方向标记（非攻击状态时）
        if self.动作 ~= "攻击" and self.动作 ~= "攻击2" then
            self.攻击方向已设置 = false
        end
    end
end


function 战斗单位类:保护事件()
  if 取两点距离(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动坐标.x,self.移动坐标.y))>=20 then
    self.移动距离=取移动坐标(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动坐标.x,self.移动坐标.y),10)
    self.角度=取两点角度(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动距离.x,self.移动距离.y))
    self.方向=角度算四方向(self.角度)
    self.动画:置方向(角度算四方向(self.角度),self.动作)
    self.显示xy.x,self.显示xy.y=self.移动距离.x,self.移动距离.y
  else
    self.角度=取两点角度(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动坐标.x,self.移动坐标.y))
    self.方向=角度算四方向(self.角度)
    self.动画:置方向(角度算四方向(self.角度),self.动作)
    self.移动开关=false
    self.保护=false
    self.移动坐标 = {}
  end
end

function 战斗单位类:友伤事件()
  if 取两点距离(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动坐标.x,self.移动坐标.y))>=100 then
    self.移动距离=取移动坐标(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动坐标.x,self.移动坐标.y),6)
    if not self.方向开关 then
    self.动作="跑去"
    self.动画:置帧率(self.动作,0.04)
      self.角度=取两点角度(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动距离.x,self.移动距离.y))
      self.方向=角度算四方向(self.角度)
      self.动画:置方向(角度算四方向(self.角度),self.动作)
      self.方向开关=true
  end
    self.显示xy.x,self.显示xy.y=self.移动距离.x,self.移动距离.y
  else
    self.角度=取两点角度(生成XY(self.显示xy.x,self.显示xy.y),生成XY(self.移动坐标.x,self.移动坐标.y))
    self.方向=角度算四方向(self.角度)
    self.动画:置方向(角度算四方向(self.角度),self.动作)
    self.移动开关=false
    self.友伤=false
    self.移动坐标 = {}
    self.方向开关=false
  end
end
function 战斗单位类:开启躲避()
  self.躲避类型 = 1
  self.停止躲避 = 75
  self.躲避开关 = true
  self.躲避数量 = 0
  self.躲避xy = {}
  self.躲避xy.x, self.躲避xy.y = self.显示xy.x, self.显示xy.y
end

function 战斗单位类:躲避事件()
  if self.躲避类型 == 1 then
    self.显示xy.x, self.显示xy.y = self.显示xy.x + self.躲避坐标, self.显示xy.y + self.躲避坐标
    self.躲避数量 = math.floor(取两点距离(生成XY(self.初始xy.x, self.初始xy.y), 生成XY(self.显示xy.x, self.显示xy.y)) / 15)
    if (取两点距离(生成XY(self.初始xy.x, self.初始xy.y), 生成XY(self.显示xy.x, self.显示xy.y)) >= self.停止躲避) then
      self.躲避类型 = 2
    end
  elseif self.躲避类型 == 2 then
    self.显示xy.x, self.显示xy.y = self.显示xy.x - self.躲避坐标, self.显示xy.y - self.躲避坐标
    self.躲避数量 = math.floor(取两点距离(生成XY(self.初始xy.x, self.初始xy.y), 生成XY(self.显示xy.x, self.显示xy.y)) / 15)
    if (取两点距离(生成XY(self.初始xy.x, self.初始xy.y), 生成XY(self.显示xy.x, self.显示xy.y)) <= 5) then
      self.显示xy.x, self.显示xy.y = self.初始xy.x, self.初始xy.y
      self.躲避类型 = 0
      self.躲避开关 = false
    end
  end
  self:躲避处理()
end

function 战斗单位类:躲避处理()
  for n = 1, self.躲避数量 do
    self.动画:显示(self.躲避xy.x + self.躲避参数 * n, self.躲避xy.y + self.躲避参数 * n, self.动作)
  end
end

function 战斗单位类:是否选中(x, y)
  if self.动画.初始化结束 == nil then
    return
  end
  return self.动画.动画[self.动作]:是否选中(x, y)
end

function 战斗单位类:置高亮()
  self.动画.动画[self.动作]:置高亮()
  if self.动画.武器 ~= nil and self.动画.武器[self.动作] ~= nil then
    self.动画.武器[self.动作]:置高亮()
  end
end

function 战斗单位类:取消高亮()
  self.动画.动画[self.动作]:取消高亮()
  if self.动画.武器 ~= nil and self.动画.武器[self.动作] ~= nil then
    self.动画.武器[self.动作]:取消高亮()
  end
end

function 战斗单位类:取状态()
  if self.移动开关 or self.击退开关 or self.击飞开关 or self.倒地开关 or self.返回开关 or self.躲避开关 or self.飞镖开关 or self.弓弩开关 or self.掉血开关 then
    return false
  else
    return true
  end
end

function 战斗单位类:更新(dt, x, y)
  if self.初始化结束 == nil then
    return
  end

  if self.人物头像 ~= nil then
    self.人物头像:更新(x, y)
    if self.人物头像:事件判断() then
      if self.状态信息 then
        self.状态信息 = false
      else
        self.状态信息 = true
      end
    end
  end
  if self.鼠标跟随 then
    self.显示xy.x, self.显示xy.y = 鼠标.x, 鼠标.y
  end
  if self.显示xy == nil then
    self.显示xy = {}
    self.显示xy.x, self.显示xy.y = 鼠标.x, 鼠标.y
  end
  if self.动作 == "待战" or self.动作 == "倒地" then
    if self.状态特效.疯狂 or self.状态特效.反间之计 or self.状态特效.发瘟匣 or self.状态特效.落魄符 then
      if self.抖动数据.开关 == false then
        self.抖动数据.开关 = true
        self.抖动数据.进程 = 1
        self.抖动数据.x, self.抖动数据.y = -0.2, -0.2
      else
        self.抖动数据.进程 = self.抖动数据.进程 + 1
        if self.抖动数据.进程 <= 5 then
          self.抖动数据.x, self.抖动数据.y = self.抖动数据.进程 - self.抖动数据.进程 * 2, self.抖动数据.进程 - self.抖动数据.进程 * 2
        else
          self.抖动数据.x, self.抖动数据.y = self.抖动数据.x + 0.2, self.抖动数据.y + 0.2
          if self.抖动数据.进程 >= 12 then
            self.抖动数据.进程 = 0
          end
        end
      end
    elseif self.抖动数据.开关 then
      self.抖动数据.进程 = 0
      self.抖动数据.x, self.抖动数据.y = 0, 0
      self.抖动数据.开关 = false
    end
  end
  if self.停止更新 and self.动画:取当前帧(self.动作) >= self.动画:取结束帧(self.动作) then
    if self.动作 == "死亡" and self.倒地开关 then
      self.倒地开关 = false
    end
  else
    self.动画:更新(dt, self.动作)
  end
  if self.动画:取当前帧(self.动作) >= self.动画:取结束帧(self.动作) then
    if self.添加状态 ~= nil and (引擎.特效库("状态_" .. self.添加状态.名称)[1] ~= nil or self:透明状态(self.添加状态.名称)) then
      local 状态参数 = self:状态前置(self.添加状态.名称)
      self.状态特效[self.添加状态.名称] = 状态参数
      if not self:透明状态(self.添加状态.名称) then
        self.状态特效[self.添加状态.名称].动画 = tp:载入特效("状态_" .. self.添加状态.名称, self:取txz(self.添加状态.名称))
      end
      self.状态特效[self.添加状态.名称].剩余回合 = { 起始 = 战斗类.回合数, 剩余回合 = self.添加状态.回合 }
      self.添加状态 = nil
    end
    if self.取消状态 ~= nil then
      self.状态特效[self.取消状态] = nil
      self.取消状态 = nil
    end
    -- 原始的结尾掉血处理被移动到返回事件和动作复原中处理
    -- if self.结尾掉血 ~= nil then
    --   self:设置掉血(self.结尾掉血, 1)
    --   self.结尾掉血 = nil
    -- end
  end
  if self.护盾 ~= nil and self.护盾 > 0 and self.状态特效 ~= nil and self.状态特效["护盾"] == nil and self.护盾开关 == false then
    self.护盾开关 = true
    self:增加状态("护盾")
  end
  if self.护盾 ~= nil and self.护盾 <= 0 and self.状态特效 ~= nil and self.状态特效["护盾"] ~= nil and self.护盾开关 == true then
    self.护盾开关 = false
    self.状态特效["护盾"] = nil
  end
  if self.法术抖动开关 ~= nil then
    self:法术受击抖动(self.法术抖动开关)
  elseif self.物理抖动开关 then
    self:物理受击抖动()
  elseif self.物理抖动开关2 then
    self:物理受击抖动()
  end
  if self.移动开关 then
    self:移动事件()
  end
  if self.保护 then
    self:保护事件()
  end
  if self.友伤 then
    self:友伤事件()
  end
  if self.击退开关 then
    self:击退处理()
  end
  if self.转圈开关 then
    self:转圈处理()
  end
  if self.飞镖开关 then
    self.飞镖动画:更新(dt)
    self.飞镖xy = 取移动坐标(self.飞镖xy, self.显示xy, 10)
    if 取两点距离(self.飞镖xy, self.显示xy) <= 20 then
      self:设置掉血(self.飞镖数据.伤害, 1)
      self:换动作("挨打", nil, true)
      self:开启击退(self.飞镖数据.死亡)
      self.飞镖开关 = nil
      self.飞镖动画 = nil
    end
  end
  if self.弓弩开关 then
    self.弓弩动画:更新(dt)
    self.弓弩xy = 取移动坐标(self.弓弩xy, self.显示xy, 10)
    if 取两点距离(self.弓弩xy, self.显示xy) <= 20 then
      self.弓弩开关 = nil
      self.弓弩动画 = nil
    end
  end
  if self.溅射开关 then
    self:设置掉血(self.溅射数据.伤害, 1)
    self:开启击退(self.溅射数据.死亡)
    self.溅射开关 = nil
  end
  if self.返回开关 then self:返回事件() end
  if self.击飞开关 then self:击飞处理() end
  if self.躲避开关 then self:躲避事件() end
  if self.动作复原 then
    if self.动画:取当前帧(self.动作) == self.动画:取结束帧(self.动作) then
        if self.动作 == "攻击" then
            self.动画:置帧率("攻击", 0.1)  -- 设置更低的攻击帧率
        end

        -- 处理施法动作的结尾气血（施法动作没有返回机制，直接在动作完成时处理）
        if self.动作 == "施法" and self.结尾掉血 ~= nil and self.结尾掉血 > 0 then
            -- 直接显示掉血
            self:设置掉血(self.结尾掉血, 1)
            self.结尾掉血 = nil
        end

        -- 处理攻击动作的结尾气血（攻击动作有返回机制，不在这里处理）
        if self.动作 == "攻击" and self.结尾掉血 ~= nil and self.结尾掉血 > 0 then
            -- 攻击动作的结尾气血在返回事件中处理，这里不处理
            -- 保留结尾掉血数据，等待返回完成后处理
        end

        self:换动作("待战")
    end
  end
  if self.抓捕开关 then
    if self.捕捉暂停 ~= nil and 时间 - self.捕捉暂停 >= 1 then
      self.捕捉暂停 = nil
      self.抓捕动画:置方向(0)
      if self.抓捕成功 then
        战斗类.战斗单位[ljcs][self.抓捕编号]:设置抓捕路径({ x = self.显示xy.x, y = self.显示xy.y }, self.抓捕成功)
      end
    end
    self.抓捕动画:更新(dt)
    if self.抓捕流程 == 1 then
      self.抓捕xy = 取移动坐标(self.抓捕xy, self.抓捕目标, 3)
      if 取两点距离(self.抓捕xy, self.抓捕目标) <= 40 then
        self.抓捕流程 = 2
        self.捕捉暂停 = 时间
      end
    elseif self.抓捕流程 == 2 and self.捕捉暂停 == nil then
      self.抓捕xy = 取移动坐标(self.抓捕xy, 生成XY(self.显示xy.x, self.显示xy.y), 3)
      if 取两点距离(self.抓捕xy, self.显示xy) <= 10 then
        self.抓捕动画 = nil
        self.抓捕开关 = false
      end
    end
  end
  if self.抓捕移动 ~= nil then
    self.显示xy = 取移动坐标(self.显示xy, self.抓捕移动, 3)
    if 取两点距离(self.抓捕移动, self.显示xy) <= 30 then
      self.是否显示 = false
      self.显示xy.x, self.显示xy.y = 1500, 1500
      self.抓捕移动 = nil
    end
  end
  if self.战斗提示 and self.战斗提示.开关 and os.time() >= self.战斗提示.停止时间 then
    self.战斗提示.开关 = false
  end
  if self.披坚开关 then
    self:披坚显示()
  end
  if self.逃跑开关 then
    self.逃跑特效:更新(dt)
    self.逃跑特效:显示(self.显示xy.x + self.抖动数据.x, self.显示xy.y + self.抖动数据.y)
  end

  -- 处理结尾气血延迟掉血
  if self.结尾掉血延迟 ~= nil then
    self.结尾掉血延迟 = self.结尾掉血延迟 - 1
    if self.结尾掉血延迟 <= 0 then
        -- 延迟时间到，显示掉血
        self:设置掉血(self.结尾掉血数值, 1)
        self.结尾掉血延迟 = nil
        self.结尾掉血数值 = nil
    end
  end

  -- if self.状态特效["霹雳弦惊"] or self.状态特效["雷怒霆激"] then
  --   -- 初始化拖影状态
  --   if not self.拖影状态 or (self.拖影状态.当前特效 ~= "霹雳弦惊" and self.状态特效["霹雳弦惊"]) or (self.拖影状态.当前特效 ~= "雷怒霆激" and self.状态特效["雷怒霆激"]) then
  --     self.拖影状态 = {
  --       历史位置 = {},
  --       最大拖影数 = 5,   -- 增加拖影数量，从3改为5
  --       透明度 = 80,     -- 降低初始透明度，从150改为80
  --       消退速度 = 400,
  --       拖影间距 = 40,    -- 减小拖影间距，从80改为40，使拖影更密集
  --       雷电特效 = {},
  --       当前特效 = self.状态特效["霹雳弦惊"] and "霹雳弦惊" or "雷怒霆激",
  --       更新计时 = 0      -- 添加更新计时器
  --     }
  --     -- 创建雷电特效
  --     for i = 1, self.拖影状态.最大拖影数 do
  --       if self.状态特效["霹雳弦惊"] then
  --         self.拖影状态.雷电特效[i] = 引擎.场景:载入特效("状态_掌心雷", 0.5)
  --       elseif self.状态特效["雷怒霆激"] then
  --         self.拖影状态.雷电特效[i] = 引擎.场景:载入特效("状态_雷怒霆激", 1.0)
  --       end
  --     end
  --   end

  --   -- 检测移动状态
  --   local 当前位置 = {x = self.显示xy.x, y = self.显示xy.y}
  --   if not self.上次位置 then
  --     self.上次位置 = 当前位置
  --   end

  --   -- 判断是否在移动
  --   local 移动距离 = 取两点距离(self.上次位置, 当前位置)
  --   self.拖影状态.更新计时 = self.拖影状态.更新计时 + dt

  --   if 移动距离 > 2 and self.拖影状态.更新计时 >= 0.05 then  -- 每0.05秒才更新一次拖影位置
  --     self.拖影状态.更新计时 = 0  -- 重置计时器
  --     -- 清空之前的拖影
  --     self.拖影状态.历史位置 = {}

  --     -- 计算移动方向角度
  --     local 角度 = math.atan2(当前位置.y - self.上次位置.y, 当前位置.x - self.上次位置.x)

  --     -- 在移动轨迹后方生成新的拖影
  --     for i = 1, self.拖影状态.最大拖影数 do
  --       local 距离 = i * self.拖影状态.拖影间距
  --       local 拖影x = 当前位置.x - math.cos(角度) * 距离
  --       local 拖影y = 当前位置.y - math.sin(角度) * 距离

  --       table.insert(self.拖影状态.历史位置, {
  --         x = 拖影x,
  --         y = 拖影y,
  --         透明度 = self.拖影状态.透明度 * (1 - (i-1)/self.拖影状态.最大拖影数)
  --       })
  --     end
  --   else  -- 角色静止
  --     -- 快速消退所有拖影
  --     for i = #self.拖影状态.历史位置, 1, -1 do
  --       local 拖影 = self.拖影状态.历史位置[i]
  --       拖影.透明度 = 拖影.透明度 - dt * self.拖影状态.消退速度
  --       if 拖影.透明度 <= 0 then
  --         table.remove(self.拖影状态.历史位置, i)
  --       end
  --     end
  --   end

  --   -- 显示拖影和雷电效果
  --   for i, 拖影 in ipairs(self.拖影状态.历史位置) do
  --     if 拖影.透明度 > 0 then
  --       -- 根据不同特效设置不同颜色
  --       if self.状态特效["霹雳弦惊"] then
  --         -- 霹雳弦惊设置为红色
  --         self.动画:置颜色(ARGB(拖影.透明度, 255, 50, 50), self.动作)
  --       else
  --         -- 雷怒霆激保持蓝色
  --         self.动画:置颜色(ARGB(拖影.透明度, 50, 150, 255), self.动作)
  --       end

  --       -- 显示拖影
  --       self.动画:显示(拖影.x + self.抖动数据.x + self.法术抖动坐标.x,
  --                   拖影.y + self.抖动数据.y + self.法术抖动坐标.y,
  --                   self.动作)
  --       -- 恢复原色
  --       self.动画:置颜色(4294967295, self.动作)

  --       -- 显示雷电特效
  --       if self.拖影状态.雷电特效[i] then
  --         self.拖影状态.雷电特效[i]:更新(dt)
  --         self.拖影状态.雷电特效[i]:显示(拖影.x + self.抖动数据.x + self.法术抖动坐标.x,
  --                                     拖影.y + self.抖动数据.y + self.法术抖动坐标.y)
  --       end
  --     end
  --   end

  --   -- 更新上次位置
  --   self.上次位置 = 当前位置
  -- else
  --   -- 清除拖影状态和特效
  --   if self.拖影状态 then
  --     -- 释放雷电特效
  --     for _, 特效 in ipairs(self.拖影状态.雷电特效) do
  --       特效:释放()
  --     end
  --     self.拖影状态 = nil
  --   end
  --   self.上次位置 = nil
  -- end
end

function 战斗单位类:换动作1(动作, 复原, 更新, 结尾气血)
  self.动作 = 动作
  self.动画:置方向(self.方向, self.动作)
  self.停止更新 = 更新
end

function 战斗单位类:抖动挨打音效()
  local 临时模型 = self.数据.模型
  if self.数据.类型 == "角色" or self.数据.类型 == "系统角色" then
    if self.数据.武器 ~= nil and 引擎.场景 ~= nil then
      if self.数据.武器.名称 == "龙鸣寒水" or self.数据.武器.名称 == "非攻" then
        local wq = "弓弩1"
        临时模型 = 临时模型 .. "_" .. wq
      else
        local wq = 引擎.场景:取武器子类(self.数据.武器.子类)
        临时模型 = 临时模型 .. "_" .. wq
      end
    end
  end
  if self.数据.变身数据 ~= nil then
    临时模型 = self.数据.变身数据
  end
  local 临时音乐 = 引擎.取音效(临时模型)
  if 临时音乐 ~= nil and 临时音乐["挨打"] ~= nil then
    tp:战斗音效类(临时音乐["挨打"], 临时音乐.资源, "挨打")
  end
end

function 战斗单位类:物理受击抖动()
  if self.模型 == "木桩" then
    self.物理抖动开关 = false
    return
  end
  if self.物理抖动开关 then
    self.法术抖动计时 = self.法术抖动计时 + 0.18
    if math.floor(self.法术抖动计时) % 2 == 0 then
      self.法术抖动坐标 = { x = 1, y = 1 }
    else
      self.法术抖动坐标 = { x = -1, y = -1 }
    end
    self:换动作1("挨打", nil, true)
  end
  if self.物理抖动开关2 then
    self.法术抖动计时 = self.法术抖动计时 + 0.18
    if math.floor(self.法术抖动计时) % 2 == 0 and self.敌我 == 1 then
      self.法术抖动坐标 = { x = 60, y = 30 }
    elseif math.floor(self.法术抖动计时) % 2 == 0 and self.敌我 == 2 then
      self.法术抖动坐标 = { x = -60, y = -30 }
    end
    self:换动作1("挨打", nil, true)
  end
end

function 战斗单位类:法术受击抖动(名称)
  if self.模型 == "木桩" then
    self.法术抖动开关 = nil
    return
  end
  if self.法术抖动开关 then
    -- 如果抖动延时不存在，说明是新的抖动，需要初始化所有变量
    if not self.抖动延时 then
      -- 优先级1：检查 skill无需抖动 表
      local 无需抖动设置 = skill无需抖动[self.法术抖动开关]
      if 无需抖动设置 then
        if 无需抖动设置 == 1 then
          -- 完全无需抖动，直接结束
          self.法术抖动开关 = nil
          self.法术抖动坐标 = { x = 0, y = 0 }
          self.法术抖动计时 = 0
          return
        else
          -- 使用 skill无需抖动 中的延迟设置
          self.抖动延时 = 无需抖动设置
        end
      else
        -- 优先级2：使用 skill法攻 表的设置
        self.抖动延时 = skill法攻[self.法术抖动开关]
      end

      -- 如果两个表都没有设置，使用默认值
      if not self.抖动延时 then
        self.抖动延时 = 10 -- 默认延迟10帧
      end

      -- 初始化抖动持续时间，防止无限抖动
      self.抖动持续时间 = 0
      self.最大抖动时间 = 120 -- 最大抖动120帧，约2秒，确保足够龙卷雨击等延迟较长的技能
      self.法术抖动计时 = 0 -- 重置计时器
    end
    
    if self.抖动延时 then
      self.抖动延时 = self.抖动延时 - 1
      if self.抖动延时 < 0 then
        self.法术抖动计时 = self.法术抖动计时 + 0.23
        self.抖动持续时间 = self.抖动持续时间 + 1

        if math.floor(self.法术抖动计时) % 2 == 0 then
          self.法术抖动坐标 = { x = 1, y = 1 }
        else
          self.法术抖动坐标 = { x = -1, y = -1 }
        end
        self:换动作1("挨打", nil, true)

        -- 检查是否达到最大抖动时间，防止无限抖动
        if self.抖动持续时间 >= self.最大抖动时间 then
          self.法术抖动开关 = nil
          self.抖动延时 = nil
          self.抖动持续时间 = nil
          self.最大抖动时间 = nil
          self.法术抖动坐标 = { x = 0, y = 0 }
        end
      end
    end
  end
end

function 战斗单位类:设置溅射(xy, 方向, 伤害, 死亡)
  self.溅射数据 = { 伤害 = 伤害, 死亡 = 死亡 }
  self.溅射开关 = true
end

function 战斗单位类:设置抓捕路径(目标, 成功)
  self:换动作("跑去")
  self.抓捕移动 = 目标
end

function 战斗单位类:设置抓捕动画(目标, 模型, 成功, 名称)
  self.抓捕资源 = 引擎.取模型(模型)
  self.抓捕动画 = 引擎.场景.资源:载入(self.抓捕资源[3], "网易WDF动画", self.抓捕资源[2])
  self.抓捕动画:置方向(2)
  self.抓捕动画:取消高亮()
  self.抓捕流程 = 1
  self.抓捕成功 = 成功
  self.抓捕目标 = { x = 战斗类.战斗单位[ljcs][目标].显示xy.x, y = 战斗类.战斗单位[ljcs][目标].显示xy.y }
  self.抓捕xy = { x = self.显示xy.x, y = self.显示xy.y }
  self.抓捕开关 = true
  self.抓捕编号 = 目标
  self.抓捕名称 = 名称
end

function 战斗单位类:取消变相(dt, x, y)
  self.动画.动画["待战"]:取消高亮()
  if self.动画.武器 ~= nil then
    self.动画.武器["待战"]:取消高亮()
  end
end

function 战斗单位类:设置提示(内容)
  self.战斗提示.开关 = true
  self.战斗提示.内容 = 内容
  self.战斗提示.停止时间 = os.time() + 2
end


function 战斗单位类:显示(dt, x, y)
  if self.初始化结束 == nil then
    return
  end
  if self.是否显示 == false then return end

  -- 处理霹雳弦惊状态的拖影效果
  if self.状态特效["霹雳弦惊"] or self.状态特效["雷怒霆激"] then
    -- 检查是否需要重新初始化拖影状态
    if not self.拖影状态 or (self.拖影状态.当前特效 ~= "霹雳弦惊" and self.状态特效["霹雳弦惊"]) or (self.拖影状态.当前特效 ~= "雷怒霆激" and self.状态特效["雷怒霆激"]) then
      self.拖影状态 = {
        历史位置 = {},    -- 存储历史位置用于拖影
        最大拖影数 = 5,   -- 最大拖影数量
        透明度 = 280,     -- 降低初始透明度，从150改为80
        消退速度 = 400,   -- 拖影消退速度
        拖影间距 = 30,    -- 每个拖影之间的距离
        雷电特效 = {},    -- 存储雷电特效
        当前特效 = self.状态特效["霹雳弦惊"] and "霹雳弦惊" or "雷怒霆激", -- 记录当前特效
        更新计时 = 0      -- 添加更新计时器
      }
      -- 为每个拖影位置创建雷电特效
      for i = 1, self.拖影状态.最大拖影数 do
        if self.状态特效["霹雳弦惊"] then
          self.拖影状态.雷电特效[i] = 引擎.场景:载入特效("状态_掌心雷", 0.5)
        elseif self.状态特效["雷怒霆激"] then
          self.拖影状态.雷电特效[i] = 引擎.场景:载入特效("状态_雷怒霆激", 1.0)  -- 使用雷击特效
        end
      end
    end

    -- 检测移动状态
    local 当前位置 = {x = self.显示xy.x, y = self.显示xy.y}
    if not self.上次位置 then
      self.上次位置 = 当前位置
    end

    -- 判断是否在移动
    local 移动距离 = 取两点距离(self.上次位置, 当前位置)
    self.拖影状态.更新计时 = self.拖影状态.更新计时 + dt

    if 移动距离 > 2 and self.拖影状态.更新计时 >= 0.05 then  -- 每0.05秒才更新一次拖影位置
      self.拖影状态.更新计时 = 0  -- 重置计时器
      -- 清空之前的拖影
      self.拖影状态.历史位置 = {}

      -- 计算移动方向角度
      local 角度 = math.atan2(当前位置.y - self.上次位置.y, 当前位置.x - self.上次位置.x)

      -- 在移动轨迹后方生成新的拖影
      for i = 1, self.拖影状态.最大拖影数 do
        local 距离 = i * self.拖影状态.拖影间距
        local 拖影x = 当前位置.x - math.cos(角度) * 距离
        local 拖影y = 当前位置.y - math.sin(角度) * 距离

        -- 添加拖影，透明度随距离递减
        table.insert(self.拖影状态.历史位置, {
          x = 拖影x,
          y = 拖影y,
          透明度 = self.拖影状态.透明度 * (1 - (i-1)/self.拖影状态.最大拖影数)
        })
      end
    else  -- 角色静止
      -- 快速消退已有拖影
      for i = #self.拖影状态.历史位置, 1, -1 do
        local 拖影 = self.拖影状态.历史位置[i]
        拖影.透明度 = 拖影.透明度 - dt * self.拖影状态.消退速度
        if 拖影.透明度 <= 0 then
          table.remove(self.拖影状态.历史位置, i)
        end
      end
    end

    -- 显示拖影和雷电效果
    for i, 拖影 in ipairs(self.拖影状态.历史位置) do
      if 拖影.透明度 > 0 then
        -- 根据不同特效设置不同颜色
        if self.状态特效["霹雳弦惊"] then
          -- 霹雳弦惊设置为红色
          self.动画:置颜色(ARGB(拖影.透明度, 255, 50, 50), self.动作)
        else
          -- 雷怒霆激保持蓝色
          self.动画:置颜色(ARGB(拖影.透明度, 50, 150, 255), self.动作)
        end

        -- 显示拖影
        self.动画:显示(拖影.x + self.抖动数据.x + self.法术抖动坐标.x,
                    拖影.y + self.抖动数据.y + self.法术抖动坐标.y,
                    self.动作)
        -- 恢复原色
        self.动画:置颜色(4294967295, self.动作)

        -- 显示雷电特效
        if self.拖影状态.雷电特效[i] then
          self.拖影状态.雷电特效[i]:更新(dt)
          self.拖影状态.雷电特效[i]:显示(拖影.x + self.抖动数据.x + self.法术抖动坐标.x,
                                      拖影.y + self.抖动数据.y + self.法术抖动坐标.y)
        end
      end
    end

    -- 更新上次位置
    self.上次位置 = 当前位置
  else
    -- 清除拖影状态和特效
    if self.拖影状态 then
      -- 释放雷电特效
      for _, 特效 in ipairs(self.拖影状态.雷电特效) do
        特效:释放()
      end
      self.拖影状态 = nil
    end
    self.上次位置 = nil
  end

  -- 显示角色本体
  if self.逃跑开关 then
    self.动作 = "返回"
  end
  if not self.躲避开关 then
    self.动画:显示(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x,
                   self.显示xy.y + self.抖动数据.y + self.法术抖动坐标.y, self.动作)
  end

  -- 其他状态特效显示、更新逻辑
  for i, v in pairs(self.状态特效) do
    if self.状态特效[i] ~= nil then
      if self.状态特效[i].cp == false and self.状态特效[i].动画 ~= nil then
        if type(self.状态特效[i].动画) == "table" and self.状态特效[i].动画[1] and self.状态特效[i].动画[2] then
          for n = 1, #self.状态特效[i].动画 do
            self.状态特效[i].动画[n]:更新(dt)
            self.状态特效[i].动画[n]:显示(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x + self.状态特效[i].py[1],
              self.显示xy.y + self.抖动数据.y + self.状态特效[i].py[2] + self.法术抖动坐标.y)
          end
        else
          self.状态特效[i].动画:更新(dt)
          self.状态特效[i].动画:显示(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x + self.状态特效[i].py[1],
            self.显示xy.y + self.抖动数据.y + self.状态特效[i].py[2] + self.法术抖动坐标.y)
        end
      end
    end
  end

  if self.飞镖开关 then
    self.飞镖动画:显示(self.飞镖xy.x, self.飞镖xy.y)
  end
  if self.弓弩开关 then
    self.弓弩动画:显示(self.弓弩xy.x, self.弓弩xy.y)
  end
  if self.共生 then
    tp.共生特效:显示(self.显示xy.x, self.显示xy.y)
  end
  for i, v in pairs(self.状态特效) do
    if self.状态特效[i] ~= nil then
      if self.状态特效[i].cp == false and self.状态特效[i].动画 ~= nil then
        if type(self.状态特效[i].动画) == "table" and self.状态特效[i].动画[1] and self.状态特效[i].动画[2] then
          for n = 1, #self.状态特效[i].动画 do
            self.状态特效[i].动画[n]:更新(dt)
            self.状态特效[i].动画[n]:显示(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x + self.状态特效[i].py[1],
              self.显示xy.y + self.抖动数据.y + self.状态特效[i].py[2] + self.法术抖动坐标.y)
          end
        else
          if self.状态特效[i].负面状态 then
            tp[self.状态特效[i].负面状态]:显示(self.显示xy.x, self.显示xy.y - self.高度 + 10)
          end
          self.状态特效[i].动画:更新(dt)
          self.状态特效[i].动画:显示(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x + self.状态特效[i].py[1],
            self.显示xy.y + self.抖动数据.y + self.状态特效[i].py[2] + self.法术抖动坐标.y)
        end
      end
    end
  end
  if self.五行珠 then
    if self.五行珠 == 1 then
      tp.五行珠特效[1]:显示(self.显示xy.x, self.显示xy.y - self.高度 - 10)
    elseif self.五行珠 == 2 then
      tp.五行珠特效[2]:显示(self.显示xy.x, self.显示xy.y - self.高度 - 10)
    elseif self.五行珠 == 3 then
      tp.五行珠特效[3]:显示(self.显示xy.x, self.显示xy.y - self.高度 - 10)
    elseif self.五行珠 == 4 then
      tp.五行珠特效[4]:显示(self.显示xy.x, self.显示xy.y - self.高度 - 10)
    elseif self.五行珠 == 5 then
      tp.五行珠特效[5]:显示(self.显示xy.x, self.显示xy.y - self.高度 - 10)
    elseif self.五行珠 >= 6 then
      tp.五行珠特效[6]:显示(self.显示xy.x, self.显示xy.y - self.高度 - 10)
    end
  end
  if self.法宝 ~= nil then
    tp.法宝特效:显示(self.显示xy.x , self.显示xy.y  - self.高度 +70)
  end
  if self.状态特效["修罗隐身"] or self.状态特效["分身术"] or self.状态特效["楚楚可怜"] then
    self.动画.动画["待战"]:置颜色(1694498815)
    if self.动画.武器 ~= nil then
      self.动画.武器["待战"]:置颜色(1694498815)
    end
  end
  if self.状态特效["兵解符"] then
    self.色相变身 = self.色相变身 + 0.7  -- 将1.5改为0.8，使闪烁速度变慢
    if self.色相变身 >= 25 then  -- 保持25作为高亮切换点
      self.动画.动画["待战"]:取消高亮()
      if self.动画.武器 ~= nil then
        self.动画.武器["待战"]:取消高亮()
      end

      -- 同步主人和宠物的状态
      if self.单位类型 == "bb" then  -- 如果是宠物
        if self.主人序号 and 战斗类.战斗单位[ljcs][self.主人序号] then
          local 主人 = 战斗类.战斗单位[ljcs][self.主人序号]
          主人.色相变身 = self.色相变身
          主人.状态特效 = 主人.状态特效 or {}
          主人.状态特效["兵解符"] = {动画=nil}
          主人.动画.动画["待战"]:取消高亮()
          if 主人.动画.武器 then
            主人.动画.武器["待战"]:取消高亮()
          end
        end
      else  -- 如果是角色
        -- 查找并同步该角色的宠物
        for i, v in pairs(战斗类.战斗单位[ljcs]) do
          if v.单位类型 == "bb" and v.主人序号 == self.编号 then
            v.色相变身 = self.色相变身
            v.状态特效 = v.状态特效 or {}
            v.状态特效["兵解符"] = {动画=nil}
            v.动画.动画["待战"]:取消高亮()
            if v.动画.武器 then
              v.动画.武器["待战"]:取消高亮()
            end
            break
          end
        end
      end

      if self.色相变身 >= 80 then  -- 将50改为80，使闪烁周期变长
        self.色相变身 = 0
        if self.单位类型 == "角色" then
          for i, v in pairs(战斗类.战斗单位[ljcs]) do
            if v.单位类型 == "bb" and v.主人序号 == self.编号 then
              v.色相变身 = 0
              break
            end
          end
        end
      end
    else
      self.动画.动画["待战"]:置高亮模式()
      if self.动画.武器 ~= nil then
        self.动画.武器["待战"]:置高亮模式()
      end

      -- 同步主人和宠物的状态
      if self.单位类型 == "bb" then  -- 如果是宠物
        if self.主人序号 and 战斗类.战斗单位[ljcs][self.主人序号] then
          local 主人 = 战斗类.战斗单位[ljcs][self.主人序号]
          主人.色相变身 = self.色相变身
          主人.状态特效 = 主人.状态特效 or {}
          主人.状态特效["兵解符"] = {动画=nil}
          主人.动画.动画["待战"]:置高亮模式()
          if 主人.动画.武器 then
            主人.动画.武器["待战"]:置高亮模式()
          end
        end
      else  -- 如果是角色
        -- 查找并同步该角色的宠物
        for i, v in pairs(战斗类.战斗单位[ljcs]) do
          if v.单位类型 == "bb" and v.主人序号 == self.编号 then
            v.色相变身 = self.色相变身
            v.状态特效 = v.状态特效 or {}
            v.状态特效["兵解符"] = {动画=nil}
            v.动画.动画["待战"]:置高亮模式()
            if v.动画.武器 then
              v.动画.武器["待战"]:置高亮模式()
            end
            break
          end
        end
      end
    end
  end
  if (self.状态特效["金刚护法"] ~= nil or self.状态特效["逆鳞"] ~= nil) and self.状态特效["修罗隐身"] == nil and self.状态特效["分身术"] == nil then
    self.色相变身 = self.色相变身 + 1
    if self.色相变身 >= 30 then
      self.动画.动画["待战"]:取消高亮()
      if self.动画.武器 ~= nil then
        self.动画.武器["待战"]:取消高亮()
      end
      if self.色相变身 >= 60 then
        self.色相变身 = 0
      end
    else
      self.动画.动画["待战"]:置高亮模式(-10855936)
      if self.动画.武器 ~= nil then
        self.动画.武器["待战"]:置高亮模式(-10855936)
      end
    end
  end
  if self.状态特效["狂怒"] ~= nil and self.状态特效["变身"] ~= nil then
    self.状态特效["变身"].动画:置颜色(ARGB(255, 205, 0, 0))
  end
  if self.逃跑开关 then
    self.动作 = "返回"
  end
  if self.躲避开关 then
  else
    self.动画:显示(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x, self.显示xy.y + self.抖动数据.y + self.法术抖动坐标.y, self.动作)
  end
  if self.战斗提示 and self.战斗提示.开关 then
    tp.字体表.战斗提示:显示(self.显示xy.x + self.抖动数据.x, self.显示xy.y + self.抖动数据.y - 120, self.战斗提示.内容)
  end
  if self.抓捕开关 then
    self.抓捕动画:显示(self.抓捕xy.x, self.抓捕xy.y)
    名称显示(self.抓捕名称, self.抓捕xy)
    引擎.场景.影子:显示(self.抓捕xy.x, self.抓捕xy.y)
  end
  for i, v in pairs(self.状态特效) do
    if self.状态特效[i] ~= nil then
      if self.状态特效[i].cp and self.状态特效[i].动画 ~= nil then
        if type(self.状态特效[i].动画) == "table" and self.状态特效[i].动画[1] and self.状态特效[i].动画[2] then
          for n = 1, #self.状态特效[i].动画 do
            self.状态特效[i].动画[n]:更新(dt)
            self.状态特效[i].动画[n]:显示(self.显示xy.x + self.抖动数据.x + self.状态特效[i].py[1],
              self.显示xy.y + self.抖动数据.y + self.状态特效[i].py[2])
          end
        else
          self.状态特效[i].动画:更新(dt)
          self.状态特效[i].动画:显示(self.显示xy.x + self.抖动数据.x + self.状态特效[i].py[1], self.显示xy.y + self.抖动数据.y +
          self.状态特效[i].py[2])
        end
      end
    end
  end
  if self.战意 then
    if self.超级战意 and self.超级战意 ~= 0 then
      if self.超级战意 >= 3 then
        tp.超级战意特效[3]:显示(self.显示xy.x, self.显示xy.y - self.高度)
      elseif self.超级战意 == 2 then
        if self.战意 == 2 then
          tp.超级战意特效[2]:显示(self.显示xy.x - 97, self.显示xy.y - self.高度 - 100)
        elseif self.战意 >= 3 then
          tp.战意特效[1]:显示(self.显示xy.x, self.显示xy.y - self.高度)
          tp.超级战意特效[2]:显示(self.显示xy.x - 97, self.显示xy.y - self.高度 - 100)
        end
      elseif self.超级战意 == 1 then
        if self.战意 == 1 then
          tp.超级战意特效[1]:显示(self.显示xy.x - 100, self.显示xy.y - self.高度 - 100)
        elseif self.战意 == 2 then
          tp.战意特效[1]:显示(self.显示xy.x, self.显示xy.y - self.高度)
          tp.超级战意特效[1]:显示(self.显示xy.x - 100, self.显示xy.y - self.高度 - 100)
        elseif self.战意 >= 3 then
          tp.战意特效[2]:显示(self.显示xy.x, self.显示xy.y - self.高度)
          tp.超级战意特效[1]:显示(self.显示xy.x - 100, self.显示xy.y - self.高度 - 100)
        end
      end
    else
      if self.战意 == 1 then
        tp.战意特效[1]:显示(self.显示xy.x, self.显示xy.y - self.高度)
      elseif self.战意 == 2 then
        tp.战意特效[2]:显示(self.显示xy.x, self.显示xy.y - self.高度)
      elseif self.战意 >= 3 then
        tp.战意特效[3]:显示(self.显示xy.x, self.显示xy.y - self.高度)
      end
    end
  end
  if self.骤雨 and self.骤雨.层数 > 0 then
    if self.骤雨.层数 == 1 then
      tp.骤雨特效[1]:显示(self.显示xy.x - 100, self.显示xy.y - 44 - self.高度)
    elseif self.骤雨.层数 == 2 then
      tp.骤雨特效[2]:显示(self.显示xy.x - 100, self.显示xy.y - 44 - self.高度)
    elseif self.骤雨.层数 >= 3 then
      tp.骤雨特效[3]:显示(self.显示xy.x - 100, self.显示xy.y - 44 - self.高度)
    end
  end
  if self.人参果 and self.人参果.枚 > 0 then
    if self.人参果.枚 == 1 then
      tp.人参果特效[1]:显示(self.显示xy.x, self.显示xy.y - self.高度)
    elseif self.人参果.枚 == 2 then
      tp.人参果特效[2]:显示(self.显示xy.x, self.显示xy.y - self.高度)
    elseif self.人参果.枚 >= 3 then
      tp.人参果特效[3]:显示(self.显示xy.x, self.显示xy.y - self.高度)
    end
  end
  名称显示(self.名称, { x = self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x, y = self.显示xy.y + self.抖动数据.y + self.法术抖动坐标.y },
    self.高亮显示)
  for n = 1, #self.攻击特效 do
    if self.攻击特效[n] ~= nil then
      if self.攻击特效[n].加速 then
        self.攻击特效[n]:更新(dt * self.攻击特效[n].加速)
      else
        self.攻击特效[n]:更新(dt)
      end
      self.攻击特效[n]:显示(self.显示xy.x, self.显示xy.y)
      if self.攻击特效[n].当前帧 == self.攻击特效[n].结束帧 then
        self.攻击特效[n] = nil
        table.remove(self.攻击特效, n)
      end
    end
  end
  self:血条显示(x, y)
  for n = 1, #self.法术特效 do
    if self.法术特效[n] ~= nil then
      -- 根据更新频率控制特效更新
      if self.法术特效[n].更新频率 then
        if self.法术特效[n].更新计数 == nil then
          self.法术特效[n].更新计数 = 0
        end
        self.法术特效[n].更新计数 = self.法术特效[n].更新计数 + 1
        if self.法术特效[n].更新计数 >= self.法术特效[n].更新频率 then
          if self.法术特效[n].加速 == nil then
            self.法术特效[n]:更新(dt * 1.1)
          else
            self.法术特效[n]:更新(self.法术特效[n].加速 * 1.1)
          end
          self.法术特效[n].更新计数 = 0
        end
      else
        if self.法术特效[n].加速 == nil then
          self.法术特效[n]:更新(dt)
        else
          self.法术特效[n]:更新(self.法术特效[n].加速)
        end
      end

      if 战斗类.掉血帧 ~= nil then
        self.法术特效[n]:显示(self.显示xy.x + self.抖动数据.x, self.显示xy.y + self.抖动数据.y - self.法术特效[n].附加y)
        if self.法术特效[n].当前帧 >= self.法术特效[n].结束帧 then
          self.法术特效[n] = nil
          战斗类.掉血帧 = nil
          table.remove(self.法术特效, n)
        end
      else
        self.法术特效[n]:显示(self.显示xy.x + self.抖动数据.x + self.法术特效[n].附加x, self.显示xy.y + self.抖动数据.y + self.法术特效[n].附加y)
        if self.法术特效[n].当前帧 == self.法术特效[n].结束帧 then
          self.法术特效[n] = nil
          table.remove(self.法术特效, n)
        end
        if 战斗类.掉血流程 ~= nil and #self.法术特效 == n and self.法术特效[n].当前帧 == floor(self.法术特效[n].结束帧 / 1.5) then 战斗类.掉血流程 = nil end
      end
    end
  end
  if self.披坚开关 then
    self:披坚显示()
  end
  if self.逃跑开关 then
    self.逃跑特效:更新(dt)
    self.逃跑特效:显示(self.显示xy.x + self.抖动数据.x, self.显示xy.y + self.抖动数据.y)
  end
end

function 战斗单位类:血条显示(x, y)
  if self.是否显示 == false then return end
  if self.敌我 == 1 then
    战斗类.血条背景:显示(floor(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x - 18),
      floor(self.显示xy.y + self.抖动数据.y + self.法术抖动坐标.y - self.高度 - 5))
    if self.气血上限 then
      战斗类.血条上限栏:置区域(0, 0, (self.气血上限 / self.最大气血) * 36, 4)
      战斗类.血条上限栏:显示(floor(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x - 17),
        floor(self.显示xy.y + self.抖动数据.y + self.法术抖动坐标.y - self.高度 - 4))
    end
    战斗类.血条栏:置区域(0, 0, (self.气血 / self.最大气血) * 36, 4)
    战斗类.血条栏:显示(floor(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x - 17),
      floor(self.显示xy.y + self.抖动数据.y + self.法术抖动坐标.y - self.高度 - 4))
  else
    if 战斗类.天地洞明 then
      战斗类.血条背景:显示(floor(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x - 18),
        floor(self.显示xy.y + self.抖动数据.y + self.法术抖动坐标.y - self.高度 - 5))
      战斗类.血条栏:置区域(0, 0, (self.气血 / self.最大气血) * 36, 4)
      战斗类.血条栏:显示(floor(self.显示xy.x + self.抖动数据.x + self.法术抖动坐标.x - 17),
        floor(self.显示xy.y + self.抖动数据.y + self.法术抖动坐标.y - self.高度 - 4))
    end
  end
  if self.特技内容开关 then
    self:特技文本显示()
  end
  self.喊话:显示(self.显示xy.x, self.显示xy.y)
end

function 战斗单位类:同步伤势(气血, 气血上限, 最大气血)
  if self.单位id == 引擎.场景.队伍[1].数字id then
    if self.气血上限 ~= nil and 气血上限 ~= nil then
      self.气血上限 = 气血上限
      引擎.场景.队伍[1].气血上限 = 气血上限
    end
  end
end

function 战斗单位类:同步气血(气血, 气血上限, 最大气血)
  if self.单位类型 == "角色" then
    if self.气血 ~= nil and 气血 ~= nil then
      self.气血 = 气血
      if self.单位id == 引擎.场景.队伍[1].数字id then
        引擎.场景.队伍[1].气血 = 气血
      end
    end
    if self.气血上限 ~= nil and 气血上限 ~= nil then
      self.气血上限 = 气血上限
      if self.单位id == 引擎.场景.队伍[1].数字id then
        引擎.场景.队伍[1].气血上限 = 气血上限
      end
    end
  elseif self.单位类型 == "bb" then
    if self.气血 ~= nil and 气血 ~= nil then
      self.气血 = 气血
      if self.单位id == 引擎.场景.队伍[1].数字id then
        引擎.场景.队伍[1].参战宝宝.气血 = 气血
      end
    end
  end
end

function 战斗单位类:结束同步(气血, 最大气血, 气血上限, 魔法, 最大魔法, 愤怒, 护盾, 战意, 五行珠, 人参果, 骤雨, 超级战意)
  if self.单位id == 引擎.场景.队伍[1].数字id then
    if self.单位类型 == "角色" then
      if self.气血 ~= nil and 气血 ~= nil then
        self.气血 = 气血
        引擎.场景.队伍[1].气血 = 气血
      end
      if 最大气血 ~= nil then
        引擎.场景.队伍[1].最大气血 = 最大气血
        self.最大气血 = 最大气血
      end
      if self.气血上限 ~= nil and 气血上限 ~= nil then
        self.气血上限 = 气血上限
        引擎.场景.队伍[1].气血上限 = 气血上限
      end
      if 魔法 ~= nil then
        引擎.场景.队伍[1].魔法 = 魔法
      end
      if 最大魔法 ~= nil then
        引擎.场景.队伍[1].最大魔法 = 最大魔法
      end
      if 愤怒 ~= nil then
        引擎.场景.队伍[1].愤怒 = 愤怒
      end
    elseif self.单位类型 == "bb" then
      if self.气血 ~= nil and 气血 ~= nil then
        self.气血 = 气血
        引擎.场景.队伍[1].参战宝宝.气血 = 气血
      end
      if 最大气血 ~= nil then
        引擎.场景.队伍[1].参战宝宝.最大气血 = 最大气血
        self.最大气血 = 最大气血
      end
      if 魔法 ~= nil then
        引擎.场景.队伍[1].参战宝宝.魔法 = 魔法
      end
      if 最大魔法 ~= nil then
        引擎.场景.队伍[1].参战宝宝.最大魔法 = 最大魔法
      end
    end
  else
    if self.气血  and 气血  then
      self.气血 = 气血
    end
    if self.最大气血 and 最大气血  then
      self.最大气血 = 最大气血
    end
    if self.气血上限  and 气血上限  then
      self.气血上限=气血上限
    end
    if  self.魔法 and 魔法  then
      self.魔法=魔法
    end
    if  self.最大魔法 and 最大魔法  then
      self.最大魔法=最大魔法
    end
    if self.愤怒 and  愤怒  then
      self.愤怒=愤怒
    end
  end
  if 护盾 ~= nil then
    self.护盾 = 护盾
  end
  if 战意 ~= nil then
    self.战意 = 战意
  end
  if 超级战意 ~= nil then
    self.超级战意 = 超级战意
  end
  if 五行珠 ~= nil then
    self.五行珠 = 五行珠
  end
  if 人参果 ~= nil then
    self.人参果 = 人参果
  end
  if 骤雨 ~= nil then
    self.骤雨 = 骤雨
  end
end

function 战斗单位类:魔法更新(魔法)
  if self.单位id == 引擎.场景.队伍[1].数字id then
    if self.单位类型 == "角色" then
      引擎.场景.队伍[1].魔法 = 魔法
      if 愤怒 ~= nil then
        引擎.场景.队伍[1].愤怒 = 愤怒
      end
    elseif self.单位类型 == "bb" then
      引擎.场景.队伍[1].参战宝宝.魔法 = 魔法
    end
  end
end

function 战斗单位类:愤怒更新(愤怒)
  if self.单位id == 引擎.场景.队伍[1].数字id then
    if self.单位类型 == "角色" then
      if 愤怒 ~= nil then
        引擎.场景.队伍[1].愤怒 = 愤怒
      end
    end
  end
end

function 战斗单位类:战意更新(战意)
  self.战意 = 战意
end

function 战斗单位类:超级战意更新(超级战意)
  self.超级战意 = 超级战意
end

function 战斗单位类:骤雨更新(骤雨)
  self.骤雨 = 骤雨
end

function 战斗单位类:五行珠更新(五行珠)
  self.五行珠 = 五行珠
end

function 战斗单位类:人参果更新(人参果)
  self.人参果 = 人参果
end

function 战斗单位类:护盾更新(护盾)
  self.护盾 = 护盾
end

function 战斗单位类:设置掉血(数额, 类型)
  local 护盾消耗 = 0
  数额 = math.floor(数额 or 1)
  if 类型 == 1 or 类型 == 3 or 类型 == 3.5 or 类型 == 4 then
    if self.护盾 ~= 0 then
      if self.护盾 >= 数额 then
        护盾消耗 = 数额
        数额 = 0
        self.护盾 = self.护盾 - 数额
        self.伤害数额 = "" .. 数额 .. "(" .. 护盾消耗 .. ")"
      else
        数额 = 数额 - self.护盾
        护盾消耗 = self.护盾
        self.伤害数额 = "" .. 数额 .. "(" .. 护盾消耗 .. ")"
        self.护盾 = 0
      end
    else
      self.伤害数额 = 数额
    end
  else
    self.伤害数额 = 数额
    if self.气血 + 数额 > self.最大气血 then
      self.伤害数额 = self.最大气血 - self.气血
    end
    if self.伤害数额 <= 0 then
      return
    end
  end
  self.伤害总数 = string.len(tostring(self.伤害数额))
  self.伤害类型 = 类型
  self.掉血开关 = true
  self.加血顺序 = 0
  if 类型 == 1 or 类型 == 3 or 类型 == 3.5 or 类型 == 4 then
    self.气血 = self.气血 - 数额
    if self.气血 <= 0 then
      self.气血 = 0
    end
    if self.单位id == 引擎.场景.队伍[1].数字id then
      if self.单位类型 == "角色" then
        引擎.场景.队伍[1].气血 = self.气血
        引擎.场景.队伍[1].最大气血 = self.最大气血
        临时愤怒 = math.floor(数额 / 引擎.场景.队伍[1].最大气血 * 0.5 * 100)
        引擎.场景.队伍[1].愤怒 = 引擎.场景.队伍[1].愤怒 + 临时愤怒
        if 引擎.场景.队伍[1].愤怒 > 150 then 引擎.场景.队伍[1].愤怒 = 150 end
      elseif self.单位类型 == "bb" then
        引擎.场景.队伍[1].参战宝宝.气血 = self.气血
        引擎.场景.队伍[1].参战宝宝.最大气血 = self.最大气血
      end
    end
  else
    self.气血 = self.气血 + 数额
    if self.气血 > self.最大气血 then
      self.气血 = self.最大气血
    end
    if self.单位id == 引擎.场景.队伍[1].数字id then
      if self.单位类型 == "角色" then
        引擎.场景.队伍[1].气血 = self.气血
        引擎.场景.队伍[1].最大气血 = self.最大气血
      elseif self.单位类型 == "bb" then
        引擎.场景.队伍[1].参战宝宝.气血 = self.气血
        引擎.场景.队伍[1].参战宝宝.最大气血 = self.最大气血
      end
    end
  end
  for n = 1, self.伤害总数 do
    self.伤害序列[n] = {}
    self.伤害序列[n].数字 = string.sub(self.伤害数额, n, n)
    if self.伤害序列[n].数字 == "(" then
      self.伤害序列[n].数字 = 10
    elseif self.伤害序列[n].数字 == ")" then
      self.伤害序列[n].数字 = 11
    end
    self.伤害序列[n].高度 = 0
    self.伤害序列[n].x = self.显示xy.x - self.伤害总数 * 12
    self.伤害序列[n].y = self.显示xy.y
  end
  self.弹跳顺序 = 1
  self.显示时间 = 0
end

function 战斗单位类:掉血显示()
  for n = 1, #self.伤害序列 do
    self.显示时间 = self.显示时间 + 1
    if n == self.弹跳顺序 then
      self.伤害序列[n].高度 = self.伤害序列[n].高度 + 4
      if (self.伤害序列[n].高度 >= 22) then
        self.弹跳顺序 = self.弹跳顺序 + 1
      end
    elseif self.伤害序列[n].高度 > 0 then
      self.伤害序列[n].高度 = self.伤害序列[n].高度 - 1.8
    end
    if (self.敌我 == 2) then
      if self.伤害序列[n] == nil or self.伤害序列[n].数字 == nil then return 0 end
      self.伤害图片[self.伤害序列[n].数字 + 1]:显示(self.伤害序列[n].x + n * 12, self.伤害序列[n].y - 40 - self.伤害序列[n].高度)
    else
      if self.伤害序列[n] == nil or self.伤害序列[n].数字 == nil then return 0 end
      if self.伤害序列[n].数字 == nil then self.伤害序列[n].数字 = 0 end
      self.伤害图片[self.伤害序列[n].数字 + 1]:显示(self.伤害序列[n].x + n * 12, self.伤害序列[n].y - 40 - self.伤害序列[n].高度)
    end
  end
  if (self.显示时间 >= #self.伤害序列 * 50) then
    self.掉血开关 = false
    self.伤害序列 = {}
  end
end

function 战斗单位类:加血显示()
  for n = 1, #self.伤害序列 do
    self.显示时间 = self.显示时间 + 1
    if n == self.弹跳顺序 then
      self.伤害序列[n].高度 = self.伤害序列[n].高度 + 4
      if (self.伤害序列[n].高度 >= 22) then
        self.弹跳顺序 = self.弹跳顺序 + 1
      end
    elseif self.伤害序列[n].高度 > 0 then
      self.伤害序列[n].高度 = self.伤害序列[n].高度 - 2
    end
    if (self.敌我 == 2) then
      if self.伤害序列[n] == nil or self.伤害序列[n].数字 == nil then return 0 end
      self.回复图片[self.伤害序列[n].数字 + 1]:显示(self.伤害序列[n].x + n * 12, self.伤害序列[n].y - 40 - self.伤害序列[n].高度)
    else
      if self.伤害序列[n] == nil or self.伤害序列[n].数字 == nil then return 0 end
      if self.伤害序列[n].数字 == nil then self.伤害序列[n].数字 = 0 end
      self.回复图片[self.伤害序列[n].数字 + 1]:显示(self.伤害序列[n].x + n * 12, self.伤害序列[n].y - 40 - self.伤害序列[n].高度)
    end
  end
  if (self.显示时间 >= #self.伤害序列 * 40) then
    self.掉血开关 = false
    self.伤害序列 = {}
  end
end

function 战斗单位类:暴击显示()
  for n = 1, #self.伤害序列 do
    self.显示时间 = self.显示时间 + 1
    if n == self.弹跳顺序 then
      self.伤害序列[n].高度 = self.伤害序列[n].高度 + 4
      if (self.伤害序列[n].高度 >= 26) then
        self.弹跳顺序 = self.弹跳顺序 + 1
      end
    elseif self.伤害序列[n].高度 > 0 then
      self.伤害序列[n].高度 = self.伤害序列[n].高度 - 2
    end
    if (self.敌我 == 2) then
      if self.伤害序列[n] == nil or self.伤害序列[n].数字 == nil then return 0 end
      self.暴击图片[self.伤害序列[n].数字 + 1]:显示(self.伤害序列[n].x + n * 12, self.伤害序列[n].y - 40 - self.伤害序列[n].高度)
    else
      if self.伤害序列[n] == nil or self.伤害序列[n].数字 == nil then return 0 end
      if self.伤害序列[n].数字 == nil then self.伤害序列[n].数字 = 0 end
      self.暴击图片[self.伤害序列[n].数字 + 1]:显示(self.伤害序列[n].x + n * 12, self.伤害序列[n].y - 40 - self.伤害序列[n].高度)
    end
  end
  if (self.显示时间 >= #self.伤害序列 * 50) then
    self.掉血开关 = false
    self.伤害序列 = {}
  end
end

function 战斗单位类:法术暴击显示()
  for n = 1, #self.伤害序列 do
    self.显示时间 = self.显示时间 + 1
    if n == self.弹跳顺序 then
      self.伤害序列[n].高度 = self.伤害序列[n].高度 + 4
      if (self.伤害序列[n].高度 >= 22) then
        self.弹跳顺序 = self.弹跳顺序 + 1
      end
    elseif self.伤害序列[n].高度 > 0 then
      self.伤害序列[n].高度 = self.伤害序列[n].高度 - 2
    end
    if (self.敌我 == 2) then
      if self.伤害序列[n] == nil or self.伤害序列[n].数字 == nil then return 0 end
      self.暴击图片[self.伤害序列[n].数字 + 1]:显示(self.伤害序列[n].x + n * 12, self.伤害序列[n].y - 40 - self.伤害序列[n].高度)
    else
      if self.伤害序列[n] == nil or self.伤害序列[n].数字 == nil then return 0 end
      if self.伤害序列[n].数字 == nil then self.伤害序列[n].数字 = 0 end
      self.暴击图片[self.伤害序列[n].数字 + 1]:显示(self.伤害序列[n].x + n * 12, self.伤害序列[n].y - 40 - self.伤害序列[n].高度)
    end
  end
  if (self.显示时间 >= #self.伤害序列 * 50) then
    self.掉血开关 = false
    self.伤害序列 = {}
  end
end

function 战斗单位类:添加特技内容(q)
  self.特技文本 = 分割字符(q)
  self.特技总数 = #self.特技文本
  self.特技序列 = {}
  self.特技次数 = 20
  self.特技间隔 = 4
  for n = 1, #self.特技文本 do
    self.特技序列[n] = {}
    self.特技序列[n].文本 = self.特技文本[n]
    self.特技序列[n].高度 = 0
    self.特技序列[n].x = self.显示xy.x - self.特技总数 * 4
    self.特技序列[n].y = self.显示xy.y
  end
  self.特技顺序 = 1
  self.特技内容开关 = true
  self.关闭计次 = 0
end

function 战斗单位类:添加披坚内容(q)
  self.披坚文本 = q
  self.关闭计次 = 0
  self.披坚开关 = true
end

function 战斗单位类:不掉血文字(q)
  self.掉血文本 = q
  self.文字坐标 = {}
  self.文字坐标.x = self.显示xy.x
  self.文字坐标.y = self.显示xy.y
  self.关闭计次 = 0
  self.图片高度 = 0
  self.其他掉血开关 = true
end

function 战斗单位类:特技文本显示()
  self.关闭计次 = self.关闭计次 + 1
  for n = 1, self.特技总数 do
    if n == self.特技顺序 then
      self.特技序列[n].高度 = self.特技序列[n].高度 + self.特技间隔
      if (self.特技序列[n].高度 >= self.特技次数) then
        self.特技顺序 = self.特技顺序 + 1
      end
    elseif self.特技序列[n].高度 > 0 then
      self.特技序列[n].高度 = self.特技序列[n].高度 - 1
    end
    self.特技文字:显示(self.特技序列[n].x - 20 + n * 15, self.特技序列[n].y - 30 - self.特技序列[n].高度, self.特技序列[n].文本)
  end
  if self.关闭计次 >= 50 then self.特技内容开关 = false end
end

function 战斗单位类:披坚显示()
  self.关闭计次 = self.关闭计次 + 1
  local qtb = 引擎.取技能(self.披坚文本)
  self.技能图标 = tp.资源:载入(qtb[6], "网易WDF动画", qtb[7])
  tp.字体表.披坚文字:置颜色(0xFFFFFF00):显示(self.显示xy.x - 29, self.显示xy.y - 149, self.披坚文本)
  self.技能图标:显示(self.显示xy.x - 23, self.显示xy.y - 122)
  战斗类.技能圈:显示(self.显示xy.x - 33, self.显示xy.y - 131)
  if self.关闭计次 >= 60 then
    self.披坚开关 = false
    self.关闭计次 = 0
    self.披坚文本 = nil
  end
end

function 战斗单位类:其他掉血显示()
  self.关闭计次 = self.关闭计次 + 1
  if self.关闭计次 >= 23 then
    self.图片高度 = self.图片高度 + 1
  else
    self.图片高度 = self.图片高度 - 1
  end
  if self.掉血文本 == "免疫" then
    self.免疫:显示(self.文字坐标.x - 29, self.文字坐标.y - 54 + self.图片高度)
  elseif self.掉血文本 == "无穷" then
    self.无穷:显示(self.文字坐标.x - 29, self.文字坐标.y - 54 + self.图片高度)
  elseif self.掉血文本 == "反弹" then
    self.反弹:显示(self.文字坐标.x - 29, self.文字坐标.y - 54 + self.图片高度)
  else
    self.躲避:显示(self.文字坐标.x - 29, self.文字坐标.y - 54 + self.图片高度)
  end
  if self.关闭计次 >= 46 then
    self.其他掉血开关 = false
    self.关闭计次 = 0
    self.掉血文本 = nil
    self.文字坐标 = {}
    self.图片高度 = 0
  end
end

-- function 战斗单位类:移动到目标(目标x, 目标y)
--   -- 计算与目标的角度
--   local 角度 = math.atan2(目标y - self.坐标.y, 目标x - self.坐标.x)
--   -- 设定战斗距离(可以根据需求调整)
--   local 战斗距离 = 80
--   -- 计算实际移动位置
--   local 移动x = 目标x - math.cos(角度) * 战斗距离
--   local 移动y = 目标y - math.sin(角度) * 战斗距离
--   -- 设置移动
--   self.移动开关 = true
--   self.移动坐标 = {x = 移动x, y = 移动y}
--   -- 设置朝向(0-3: 东北、西北、西南、东南)
--   self.方向 = 角度算四方向(math.deg(角度))
--   self:换动作("跑去")
-- end

function 战斗单位类:暂停(时间)
  self.暂停开关 = true
  self.暂停时间 = 时间
  self.暂停开始 = os.clock()
end

function 战斗单位类:更新暂停()
  if self.暂停开关 then
    if os.clock() - self.暂停开始 >= self.暂停时间 then
      self.暂停开关 = false
      self.暂停时间 = nil
      self.暂停开始 = nil
      return true
    end
    return false
  end
  return true
end



return 战斗单位类

