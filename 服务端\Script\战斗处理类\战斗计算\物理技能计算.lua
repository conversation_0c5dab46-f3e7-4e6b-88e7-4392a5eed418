-- @Author: baidwwy
-- @Date:   2024-05-13 00:08:03
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-13 13:36:48

local 物理技能计算 = class()
local jhf = string.format
local random = math.random
local qz=math.floor
local sj = 取随机数
function 物理技能计算:初始化()
end
function 物理技能计算:基础计算(编号,名称,等级,战斗数据,目标,目标数)
	local 返回=nil
	local 重复攻击=false
	local 重复次数=nil
	local 起始伤害=1
	local 叠加伤害=0
	local 重复提示=false
	local 允许保护=true
	if 名称=="烟雨剑法" then
		目标数=2
		重复攻击=true
		起始伤害= 1
		if 战斗数据:取经脉(编号, "五庄观","起雨") and 战斗数据.参战单位[编号].骤雨.层数>=3 then
			目标数=3
			战斗数据.参战单位[编号].骤雨.层数=0
		end
	elseif 名称 == "天命剑法" then
	    起始伤害 = 1.1
	    叠加伤害 = -0.1
	    local 随机数 = 取随机数(1, 10)
	    if 随机数 <= 4 then
	        目标数 = 7
	    elseif 随机数 <= 9 then
	        目标数 = 取随机数(5, 6)
	    else
	        目标数 = 取随机数(1, 4)
	    end
	    重复攻击 = true
	elseif 名称=="敲金击玉" then
		local jc = 0.3
		if 战斗数据.参战单位[编号].道果 then
			jc = 0.2
		elseif 战斗数据.参战单位[编号].饮露 then
			jc = 0.34
		end
		起始伤害 = 1 + 战斗数据.参战单位[编号].人参果.枚*jc
		if 战斗数据:取经脉(编号, "五庄观","木摧") then
			起始伤害 = 起始伤害 * 1.05
		end
	elseif 名称=="落土止息" then
		local jc = 0.3
		if 战斗数据.参战单位[编号].道果 then
			jc = 0.2
		elseif 战斗数据.参战单位[编号].饮露 then
			jc = 0.34
		end
		起始伤害 = 1 + 战斗数据.参战单位[编号].人参果.枚*jc*2
	elseif 名称 == "高级连击" then
	    if (战斗数据.参战单位[编号].名称 == "天杀星·任来聘" or 战斗数据.参战单位[编号].名称 == "侍女") and 战斗数据.参战单位[编号].队伍 == 0 then
	        目标数 = 6
	    else
	        目标数 = 2
	    end
	    重复攻击 = true
	elseif 名称=="理直气壮" then
		目标数=3
		重复攻击=true
	elseif 名称=="乘胜追击" then
		起始伤害=0.4
		目标数=2
		重复攻击=true
	elseif 名称=="剑荡四方" then
		起始伤害=0.8
		叠加伤害=-0.2
	elseif 名称=="水击三千" then
		起始伤害=1.1
	elseif 名称=="惊心一剑" then
		起始伤害=1.1
	elseif 名称=="牛刀小试" then
		返回=true
		起始伤害=1.2
	-- elseif 名称=="横扫千军" then
	-- 	起始伤害=0.85
	-- 	叠加伤害=0.15
	-- 	目标数=3
	-- 	重复攻击=true
	-- 	if 战斗数据:取经脉(编号, "大唐官府","无敌") then
	-- 		起始伤害=0.75
	-- 		目标数=4
	-- 	end
	-- 	if 战斗数据:取经脉(编号,"大唐官府", "突刺") and 战斗数据.参战单位[编号].气血>=战斗数据.参战单位[编号].最大气血*0.9 then
	-- 		起始伤害 = 起始伤害 + 0.05
	-- 	end
	elseif 名称 == "横扫千军" then
	    起始伤害 = 0.85
	    叠加伤害 = 0.15
	    目标数 = 3
	    重复攻击 = true
	    if 战斗数据:取经脉(编号, "大唐官府", "无敌") or ((战斗数据.参战单位[编号].名称 == "水火不侵" or 战斗数据.参战单位[编号].名称 == "天剑星·王虎") and 战斗数据.参战单位[编号].队伍== 0) then
	        起始伤害 = 0.75
	        目标数 = 4
	        叠加伤害 = 0.1  -- 修改叠加伤害为0.1
	    end
	    if 战斗数据:取经脉(编号, "大唐官府", "突刺") and 战斗数据.参战单位[编号].气血 >= 战斗数据.参战单位[编号].最大气血 * 0.9 then
	        起始伤害 = 起始伤害 + 0.05
	    end
	elseif 名称=="破釜沉舟" then
		重复提示=true
		返回=true
	elseif 名称=="披挂上阵" then
		起始伤害=0.85
		叠加伤害=0.1
		目标数=3
		重复攻击=true
	elseif 名称=="翩鸿一击" then
		起始伤害=1
		if 战斗数据:取经脉(编号, "大唐官府","翩鸿") then
			起始伤害=1.35
		end
	elseif 名称=="长驱直入" then
		起始伤害=1.1
	elseif 名称=="摧枯拉朽" then
		起始伤害=1.1
	elseif 名称=="浪涌" then
		起始伤害=0.75
		if 战斗数据:取经脉(编号, "凌波城","战诀") and not 战斗数据.PK战斗 then
			起始伤害=0.85
		else
			战斗数据:增加战意(编号,1)
		end
	elseif 名称=="裂石" then
		if 战斗数据:取被动(编号,"吞山") then
			战斗数据.参战单位[编号].附加必杀=5
		end
	elseif 名称=="腾雷" then
		if not 战斗数据:取经脉(编号, "凌波城","闪雷") then
			if 战斗数据.参战单位[编号].超级战意>=1 then
				战斗数据.参战单位[编号].超级战意 = 战斗数据.参战单位[编号].超级战意 - 1
				if 战斗数据:取经脉(编号, "凌波城","妙得") then
					战斗数据.参战单位[编号].附加必杀 = 战斗数据.参战单位[编号].附加必杀 + 20
				else
					起始伤害 = 起始伤害 * 1.2
					if 战斗数据:取经脉(编号, "凌波城","惊涛") then
						local num = 0
						if 战斗数据:取装备五行(编号,3)=="水" then
							num = num + 0.04
						end
						if 战斗数据:取装备五行(编号,4)=="水" then
							num = num + 0.04
						end
						起始伤害 = 起始伤害 * (1 + num)
					end
				end
				if 战斗数据:取经脉(编号,"凌波城","乘势") then
					战斗数据.参战单位[编号].乘势=1
				end
			end
		end
	elseif 名称=="惊涛怒" then
		起始伤害=0.7
		if 等级 >= 175 then
			起始伤害=1
		elseif 等级 >= 150 then
			起始伤害=0.95
		elseif 等级 >= 125 then
			起始伤害=0.9
		elseif 等级 >= 100 then
			起始伤害=0.85
		elseif 等级 >= 75 then
			起始伤害=0.8
		elseif 等级 >= 50 then
			起始伤害=0.75
		end
		if 战斗数据.参战单位[编号].法术状态.再战 then
			起始伤害 = 起始伤害 * 1.15
		end
		if 战斗数据.参战单位[编号].超级战意>=1 then
			战斗数据.参战单位[编号].超级战意 = 战斗数据.参战单位[编号].超级战意 - 1
			if 战斗数据:取经脉(编号,"凌波城","乘势") then
				战斗数据.参战单位[编号].乘势=1
			end
			if 战斗数据:取经脉(编号, "凌波城","妙得") then
				战斗数据.参战单位[编号].附加必杀 = 战斗数据.参战单位[编号].附加必杀 + 20
			else
				起始伤害 = 起始伤害 * 1.2
				if 战斗数据:取经脉(编号, "凌波城","惊涛") then
					local num = 0
					if 战斗数据:取装备五行(编号,3)=="水" then
						num = num + 0.04
					end
					if 战斗数据:取装备五行(编号,4)=="水" then
						num = num + 0.04
					end
					起始伤害 = 起始伤害 * (1 + num)
				end
			end
		end
	elseif 名称=="断岳势" then
		目标数=2
		重复攻击=true
		起始伤害=1
		叠加伤害=-0.2
		if 战斗数据:取被动(编号,"吞山") then
			战斗数据.参战单位[编号].附加必杀 = 战斗数据.参战单位[编号].附加必杀 + 5
		end
		if 战斗数据.参战单位[编号].法术状态.再战 then
			起始伤害 = 起始伤害 * 1.15
		end
		if 战斗数据.参战单位[编号].超级战意>=1 then
			战斗数据.参战单位[编号].超级战意 = 战斗数据.参战单位[编号].超级战意 - 1
			if 战斗数据:取经脉(编号,"凌波城","乘势") then
				战斗数据.参战单位[编号].乘势=1
			end
			if 战斗数据:取经脉(编号, "凌波城","妙得") then
				战斗数据.参战单位[编号].附加必杀 = 战斗数据.参战单位[编号].附加必杀 + 20
			else
				起始伤害 = 起始伤害 * 1.2
				if 战斗数据:取经脉(编号, "凌波城","惊涛") then
					local num = 0
					if 战斗数据:取装备五行(编号,3)=="水" then
						num = num + 0.04
					end
					if 战斗数据:取装备五行(编号,4)=="水" then
						num = num + 0.04
					end
					起始伤害 = 起始伤害 * (1 + num)
				end
			end
		end
	elseif 名称=="天崩地裂" then
		目标数=3
		重复攻击=true
		起始伤害= 1.1
		叠加伤害=-0.1
		if 战斗数据:取被动(编号,"吞山") then
			战斗数据.参战单位[编号].附加必杀=5
		end
		if 战斗数据.参战单位[编号].天神怒斩~=nil then
			for k, v in pairs(战斗数据.参战单位[编号].主动技能) do
				if v.名称 == "天神怒斩" and v.剩余冷却回合~=nil and v.剩余冷却回合>0 then
					v.剩余冷却回合=v.剩余冷却回合-1
					break
				end
			end
		end
		if 战斗数据.参战单位[编号].超级战意>=1 then
			local num = 战斗数据.参战单位[编号].超级战意
			战斗数据.参战单位[编号].超级战意 = 0
			if 战斗数据:取经脉(编号,"凌波城","乘势") then
				战斗数据.参战单位[编号].乘势=1
			end
			if 战斗数据:取经脉(编号, "凌波城","妙得") then
				战斗数据.参战单位[编号].附加必杀 = 战斗数据.参战单位[编号].附加必杀 + 20*num
			else
				起始伤害 = 起始伤害 * (1+0.2*num)
				if 战斗数据:取经脉(编号, "凌波城","惊涛") then
					local num = 0
					if 战斗数据:取装备五行(编号,3)=="水" then
						num = num + 0.04
					end
					if 战斗数据:取装备五行(编号,4)=="水" then
						num = num + 0.04
					end
					起始伤害 = 起始伤害 * (1 + num)
				end
			end
		end
	elseif 名称=="翻江搅海" then
		起始伤害=0.8
		返回=true
		if 等级 >= 100 then
			起始伤害=1
		elseif 等级 >= 50 then
			起始伤害=0.9
		end
		if 战斗数据.参战单位[编号].天神怒斩~=nil then
			for k, v in pairs(战斗数据.参战单位[编号].主动技能) do
				if v.名称 == "天神怒斩" and v.剩余冷却回合~=nil and v.剩余冷却回合>0 then
					v.剩余冷却回合=v.剩余冷却回合-1
					break
				end
			end
		end
		if 战斗数据.参战单位[编号].超级战意>=1 then
			local num = 战斗数据.参战单位[编号].超级战意
			战斗数据.参战单位[编号].超级战意 = 0
			if 战斗数据:取经脉(编号,"凌波城","乘势") then
				战斗数据.参战单位[编号].乘势=1
			end
			if 战斗数据:取经脉(编号, "凌波城","妙得") then
				战斗数据.参战单位[编号].附加必杀 = 战斗数据.参战单位[编号].附加必杀 + 20*num
			else
				起始伤害 = 起始伤害 * (1+0.2*num)
				if 战斗数据:取经脉(编号, "凌波城","惊涛") then
					local num = 0
					if 战斗数据:取装备五行(编号,3)=="水" then
						num = num + 0.04
					end
					if 战斗数据:取装备五行(编号,4)=="水" then
						num = num + 0.04
					end
					起始伤害 = 起始伤害 * (1 + num)
				end
			end
		end
	elseif 名称=="天神怒斩" then
		起始伤害=1
		if 战斗数据.参战单位[编号].天神怒斩==nil then
			战斗数据.参战单位[编号].天神怒斩=0
		end
		if 战斗数据.参战单位[编号].战意~=1 then
			战斗数据.参战单位[编号].天神怒斩=战斗数据.参战单位[编号].天神怒斩+0.25
		else
			战斗数据.参战单位[编号].天神怒斩=战斗数据.参战单位[编号].天神怒斩+0.5
		end
		起始伤害 = 起始伤害 * (1+战斗数据.参战单位[编号].天神怒斩)
	elseif 名称=="满天花雨" then
		起始伤害=1.1
		if 战斗数据.参战单位[编号].法术状态.自矜 and 战斗数据.参战单位[编号].JM.当前流派=="花间美人" then
			目标数=2
			重复次数=2
			重复攻击=true
		end
	elseif 名称=="葬玉焚花" then
		起始伤害=0.9
		if 战斗数据:取经脉(编号, "女儿村", "花落") then
			起始伤害 = 0.94
		end
		if 战斗数据.参战单位[编号].法术状态.自矜 and 战斗数据.参战单位[编号].JM.当前流派=="花间美人" then
			目标数=8
			重复次数=2
			重复攻击=true
			重复提示=true
		end
	elseif 名称=="连环击" then
		目标数=math.floor(等级/35)+2
		重复攻击=true
		起始伤害=1
		叠加伤害=-0.1
		if 战斗数据.参战单位[编号].法术状态.肝胆 then
			起始伤害 = 起始伤害 * 1.15
			战斗数据.参战单位[编号].法术状态.肝胆.触发=true
		end
	elseif 名称=="狮搏" then
		起始伤害=1.4
		if 战斗数据:取经脉(编号, "狮驼岭","威压") and 战斗数据:取目标类型(目标[1]) == "召唤兽" then
			起始伤害=起始伤害*1.2
		end
	elseif 名称=="蚩尤之搏" then
		起始伤害=1.4
		if 战斗数据:取经脉(编号, "狮驼岭","威压") and 战斗数据:取目标类型(目标[1]) == "召唤兽" then
			起始伤害=起始伤害*1.2
		end
	elseif 名称=="鹰击" then
		起始伤害=1.1
		重复提示=true
		返回=true
		if 战斗数据.参战单位[编号].九天~=nil then
			起始伤害 = 起始伤害*1.15
		end
		if 战斗数据.参战单位[编号].法术状态.肝胆 then
			起始伤害 = 起始伤害 * 1.15
			战斗数据.参战单位[编号].法术状态.肝胆.触发=true
		end
		-- if 战斗数据.参战单位[编号].法术状态.狂怒 then
		-- 	起始伤害 = 起始伤害 * 1.05
		-- end
	elseif 名称=="疯狂鹰击" then
		起始伤害=1.15
		重复提示=true
		返回=true
	elseif 名称=="困兽之斗" then
		起始伤害=1.4
		if 战斗数据.参战单位[编号].法术状态.狂怒 and sj()<=50 then
			目标[2]=目标[1]
			目标数=#目标
			重复提示=true
			返回=true
		end
	elseif 名称=="百爪狂杀" then
		重复提示=true
		返回=true
		起始伤害=0.9
	elseif 名称=="六道无量" or 名称=="无赦咒令" or 名称=="血影蚀心" then
		起始伤害=1.1
	elseif 名称=="百鬼噬魂" then
		起始伤害=0.8
	elseif 名称=="生杀予夺" then
		起始伤害=1.3
	elseif 名称=="千蛛噬魂" then
		起始伤害=0.8
	elseif 名称=="蛛丝缠绕" then
		起始伤害=1.1
	elseif 名称=="绝命毒牙" then
		起始伤害=1.25
	elseif 名称=="日光耀" then
		起始伤害=1.1
		if 战斗数据.参战单位[编号].法术状态["剑意莲心"] then
			if 战斗数据:消耗五行珠(编号) then
				目标数=2
				重复攻击=true
			end
		else
			local gl = 50
			if 战斗数据.参战单位[编号].五行珠>=3 and 战斗数据:取经脉(编号, "普陀山", "相生") then
				gl = 75
			end
			if 战斗数据:取五行克制("金",战斗数据.参战单位[目标[1]].防御五行) or (战斗数据.参战单位[编号].五行珠<=0 and 战斗数据:取经脉(编号, "普陀山", "执念")) then
				gl = 100
			end
			if sj()<=gl then
				战斗数据:增加五行珠(编号)
			end
		end
		if 战斗数据.参战单位[编号].攻击五行 == "金" then
			起始伤害=1.2
		end
		if 战斗数据:取经脉(编号, "普陀山", "因缘") and 战斗数据:取五行克制("金",战斗数据.参战单位[目标[1]].防御五行) then
			起始伤害=起始伤害*1.05
		end
		if 战斗数据:取经脉(编号, "普陀山", "万象") and 战斗数据.参战单位[编号].法术状态.颠倒五行 then
			起始伤害=起始伤害*1.08
		end
	elseif 名称=="靛沧啸" then
		起始伤害=1.1
		if 战斗数据.参战单位[编号].法术状态["剑意莲心"] then
			if 战斗数据:消耗五行珠(编号) then
				目标数=2
				重复攻击=true
			end
		else
			local gl = 50
			if 战斗数据.参战单位[编号].五行珠>=3 and 战斗数据:取经脉(编号, "普陀山", "相生") then
				gl = 75
			end
			if 战斗数据:取五行克制("金",战斗数据.参战单位[目标[1]].防御五行) or (战斗数据.参战单位[编号].五行珠<=0 and 战斗数据:取经脉(编号, "普陀山", "执念")) then
				gl = 100
			end
			if sj()<=gl then
				战斗数据:增加五行珠(编号)
			end
		end
		if 战斗数据.参战单位[编号].攻击五行 == "水" then
			起始伤害=1.2
		end
		if 战斗数据:取经脉(编号, "普陀山", "因缘") and 战斗数据:取五行克制("水",战斗数据.参战单位[目标[1]].防御五行) then
			起始伤害=起始伤害*1.05
		end
		if 战斗数据:取经脉(编号, "普陀山", "万象") and 战斗数据.参战单位[编号].法术状态.颠倒五行 then
			起始伤害=起始伤害*1.08
		end
	elseif 名称=="巨岩击" then
		起始伤害=1.1
		if 战斗数据.参战单位[编号].法术状态["剑意莲心"] then
			if 战斗数据:消耗五行珠(编号) then
				目标数=2
				重复攻击=true
			end
		else
			local gl = 50
			if 战斗数据.参战单位[编号].五行珠>=3 and 战斗数据:取经脉(编号, "普陀山", "相生") then
				gl = 75
			end
			if 战斗数据:取五行克制("金",战斗数据.参战单位[目标[1]].防御五行) or (战斗数据.参战单位[编号].五行珠<=0 and 战斗数据:取经脉(编号, "普陀山", "执念")) then
				gl = 100
			end
			if sj()<=gl then
				战斗数据:增加五行珠(编号)
			end
		end
		if 战斗数据.参战单位[编号].攻击五行 == "土" then
			起始伤害=1.2
		end
		if 战斗数据:取经脉(编号, "普陀山", "因缘") and 战斗数据:取五行克制("土",战斗数据.参战单位[目标[1]].防御五行) then
			起始伤害=起始伤害*1.05
		end
		if 战斗数据:取经脉(编号, "普陀山", "万象") and 战斗数据.参战单位[编号].法术状态.颠倒五行 then
			起始伤害=起始伤害*1.08
		end
	elseif 名称=="苍茫刺" then
		起始伤害=1.1
		if 战斗数据.参战单位[编号].法术状态["剑意莲心"] then
			if 战斗数据:消耗五行珠(编号) then
				目标数=2
				重复攻击=true
			end
		else
			local gl = 50
			if 战斗数据.参战单位[编号].五行珠>=3 and 战斗数据:取经脉(编号, "普陀山", "相生") then
				gl = 75
			end
			if 战斗数据:取五行克制("金",战斗数据.参战单位[目标[1]].防御五行) or (战斗数据.参战单位[编号].五行珠<=0 and 战斗数据:取经脉(编号, "普陀山", "执念")) then
				gl = 100
			end
			if sj()<=gl then
				战斗数据:增加五行珠(编号)
			end
		end
		if 战斗数据.参战单位[编号].攻击五行 == "木" then
			起始伤害=1.2
		end
		if 战斗数据:取经脉(编号, "普陀山", "因缘") and 战斗数据:取五行克制("木",战斗数据.参战单位[目标[1]].防御五行) then
			起始伤害=起始伤害*1.05
		end
		if 战斗数据:取经脉(编号, "普陀山", "万象") and 战斗数据.参战单位[编号].法术状态.颠倒五行 then
			起始伤害=起始伤害*1.08
		end
	elseif 名称=="地裂焚" then
		起始伤害=1.1
		if 战斗数据.参战单位[编号].法术状态["剑意莲心"] then
			if 战斗数据:消耗五行珠(编号) then
				目标数=2
				重复攻击=true
			end
		else
			local gl = 50
			if 战斗数据.参战单位[编号].五行珠>=3 and 战斗数据:取经脉(编号, "普陀山", "相生") then
				gl = 75
			end
			if 战斗数据:取五行克制("金",战斗数据.参战单位[目标[1]].防御五行) or (战斗数据.参战单位[编号].五行珠<=0 and 战斗数据:取经脉(编号, "普陀山", "执念")) then
				gl = 100
			end
			if sj()<=gl then
				战斗数据:增加五行珠(编号)
			end
		end
		if 战斗数据.参战单位[编号].攻击五行 == "火" then
			起始伤害=1.2
		end
		if 战斗数据:取经脉(编号, "普陀山", "因缘") and 战斗数据:取五行克制("火",战斗数据.参战单位[目标[1]].防御五行) then
			起始伤害=起始伤害*1.05
		end
		if 战斗数据:取经脉(编号, "普陀山", "万象") and 战斗数据.参战单位[编号].法术状态.颠倒五行 then
			起始伤害=起始伤害*1.08
		end
	elseif 名称=="天雷斩" then
		起始伤害 = 1 - 目标数*0.05
		if 战斗数据:取目标类型(目标) ~= "玩家" then
			起始伤害 = 1.25
		end
		重复提示=true
		--返回=true
	elseif 技能名称=="风雷斩" then
		起始伤害 = 1 - 目标数*0.05
		if 起始伤害<=0.85 then
			起始伤害=0.85
		end
	elseif 技能名称=="风雷斩·飞霆" then --和天雷斩区别是PK可以多秒两个单位
		起始伤害= 1 - 目标数*0.05
		if 起始伤害<=0.8 then
			起始伤害=0.8
		end
		重复提示=true
		返回=true
	elseif 技能名称=="风雷斩·霹雳" then
		起始伤害=1.35
		if 战斗数据.参战单位[编号].法术状态.天雷灌注 and 取随机数()<=65 then
			目标数=2
			重复攻击=true
		end
	elseif 技能名称=="威仪九霄" then
		起始伤害=0.1*(1+0.03)^战斗数据.参战单位[编号].法术状态.威仪九霄.层数
		重复提示=true
		返回=true
		战斗数据.参战单位[编号].法术状态.威仪九霄.层数=0
	elseif 名称=="破血狂攻" then
		目标[2]=目标[1]
		目标数=#目标
		起始伤害=1
		if 战斗数据:取经脉(编号, "五庄观", "强击") then
			起始伤害 = 1.32
		end
	elseif 名称=="破碎无双" then
		起始伤害=1
		if 战斗数据:取经脉(编号, "五庄观", "强击") then
			起始伤害 = 1.32
		end
	elseif 名称=="武神之怒" then
		起始伤害=1
		叠加伤害=0.1
		目标数=99
		重复攻击=true
	elseif 名称=="怒击" then
		起始伤害=1
		叠加伤害=0.1
		目标数=1
	elseif 名称=="猛击" then
		起始伤害=1
		叠加伤害=0.1
		目标数=2
		重复攻击=true
	end


	return 返回,重复攻击,重复次数,起始伤害,叠加伤害,重复提示,允许保护,目标数
end
function 物理技能计算:物攻技能计算(编号,名称,等级,战斗数据)
	if 名称=="后发制人" then
		if 战斗数据.参战单位[编号].气血<=0 then
			战斗数据:取消状态(名称,战斗数据.参战单位[编号])
			战斗数据.战斗流程[#战斗数据.战斗流程+1]={流程=610,攻击方=编号,状态="后发制人",挨打方={{挨打方=1}}}
			return
		else
			if not 战斗数据:取目标状态(编号,战斗数据.参战单位[编号].法术状态.后发制人.目标,1) then
				战斗数据.参战单位[编号].指令.目标 = 战斗数据:取单个敌方目标(编号)
			else
				战斗数据.参战单位[编号].指令.目标=战斗数据.参战单位[编号].法术状态.后发制人.目标
			end
		end
	end
	if 名称=="风雷斩" and 战斗数据:取目标类型(编号) == "玩家" then
		if 战斗数据.参战单位[编号].法术状态.雷怒霆激 and 战斗数据.参战单位[编号].法术状态.霹雳弦惊 then
		elseif 战斗数据.参战单位[编号].法术状态.雷怒霆激 then
			名称="风雷斩·飞霆"
		elseif 战斗数据.参战单位[编号].法术状态.霹雳弦惊 then
			名称="风雷斩·霹雳"
		end
		if 战斗数据.参战单位[编号].法术状态.天雷灌注 then
	          战斗数据.参战单位[编号].法术状态.天雷灌注.层数 = 战斗数据.参战单位[编号].法术状态.天雷灌注.层数 - 1
	          if 战斗数据.参战单位[编号].法术状态.天雷灌注.层数<=0 then
	             战斗数据:取消状态("天雷灌注", 战斗数据.参战单位[编号])
	          end
	    end
	end
	local 目标=战斗数据.参战单位[编号].指令.目标
	local zza = skill.数据[名称]
	local 目标数 = zza.技能人数(等级,战斗数据.参战单位[编号],战斗数据.PK战斗)
	local 目标=战斗数据:取多个敌方目标(编号,目标,目标数)
	local 战意提示=false
	local 返回信息
	if #目标==0 then return end
	for n=1,#目标 do
		if 战斗数据.参战单位[目标[n]].法术状态.分身术~=nil and 战斗数据.参战单位[目标[n]].法术状态.分身术.破解==nil then
			战斗数据.参战单位[目标[n]].法术状态.分身术.破解=1
			table.remove(目标,n)
			break
		end
	end
	if #目标==0 then return end
	目标数=#目标
	local 消耗1,类型1 = zza.技能消耗(目标数,战斗数据.参战单位[编号])
	if 初始技能计算:技能消耗(战斗数据,战斗数据.参战单位[编号],消耗1,类型1,名称)==false then
		战斗数据.战斗流程[#战斗数据.战斗流程+1]={流程=6,战斗提示={内容="行动失败",编号=编号}}
		return
	end
	if 战斗数据:取目标类型(编号) == "召唤兽" then
		战斗技能:顾盼生姿(战斗数据,编号)
	end
	if 名称 == "花谢花飞" then
		名称="满天花雨"
		战斗数据.参战单位[编号].花谢花飞=1
	end
	local 返回,重复攻击,重复次数,起始伤害,叠加伤害,重复提示,允许保护,目标数 = self:基础计算(编号,名称,等级,战斗数据,目标,目标数)
	if 战斗数据.参战单位[编号].指令.伤害系数 then
        起始伤害 = 起始伤害 * 战斗数据.参战单位[编号].指令.伤害系数
    end
	local mcjc=nil
	if 重复攻击 then
		if 重复次数==nil then
			local 临时目标=目标[1]
			for n=1,目标数 do
				目标[n]=临时目标
			end
		else
			mcjc=名称
			local num = 1
			local S_目标=DeepCopy(目标)
			for n=1,目标数 do
				local 临时目标=S_目标[num]
				if n%重复次数== 0 then
					num = num + 1
				end
				目标[n]=临时目标
			end
		end
	end
	local 战斗终止=false
	local 寄存={}
	for n=1,目标数 do
		寄存[n]=目标[n]
	end
	local cs = 目标数
	if 重复次数~=nil then
		for n=1,目标数 do
			if 战斗终止==false then
				local go = true
				if n%重复次数== 0 and 战斗数据:取目标状态(编号,目标[n],1)==false then
					go = false
				end
				if go then
					if n%重复次数== 0 then
						名称="自矜"
					else
						名称=mcjc
					end
					self:物攻技能计算1(编号,目标[n],起始伤害,叠加伤害,返回,允许保护,n,名称,战斗数据)
					if n == 1 then
						战斗数据.战斗流程[#战斗数据.战斗流程].战斗法术 = 名称
						战斗数据.战斗流程[#战斗数据.战斗流程].寄存目标 = 寄存
					end
					战斗数据.战斗流程[#战斗数据.战斗流程].提示={类型="法术",名称=战斗数据.参战单位[编号].名称.."使用了"..名称,允许=true}
					if n==目标数 then
						战斗数据.战斗流程[#战斗数据.战斗流程].返回=true
					elseif 战斗数据:取行动状态(编号)==false then
						战斗数据.战斗流程[#战斗数据.战斗流程].返回=true
						战斗终止=true
					elseif 战斗数据:取目标状态(编号,目标[n+1],1)==false then
						战斗数据.战斗流程[#战斗数据.战斗流程].返回=true
						战斗终止=true
					end
				else
					if n==目标数 then
						战斗数据.战斗流程[#战斗数据.战斗流程].返回=true
					elseif 战斗数据:取行动状态(编号)==false then
						战斗数据.战斗流程[#战斗数据.战斗流程].返回=true
						战斗终止=true
					elseif 战斗数据:取目标状态(编号,目标[n+1],1)==false then
						战斗数据.战斗流程[#战斗数据.战斗流程].返回=true
						战斗终止=true
					end
				end
			end
		end
	else
		for n=1,目标数 do
			if 战斗终止==false then
				self:物攻技能计算1(编号,目标[n],起始伤害,叠加伤害,返回,允许保护,n,名称,战斗数据)
				cs = n
				local 提示内容x = ""
				if 名称~= "高级连击" and 名称~= "理直气壮" then
					提示内容x = 战斗数据.参战单位[编号].名称.."使用了"..名称
				end
				if 名称=="武神之怒" and n == 1 then
					战斗数据.战斗流程[#战斗数据.战斗流程].全屏法术 = 名称
				elseif (名称=="翻江搅海" or 名称=="神针撼海") and n == 1 then
					战斗数据.战斗流程[#战斗数据.战斗流程].全屏法术 = 名称
				elseif n == 1 and (名称=="力劈华山" or 名称=="满天花雨" or 名称=="惊涛怒" or 名称=="同伤式" or 名称=="葬玉焚花" or 名称=="千蛛噬魂" or 名称=="蛛丝缠绕" ) then
					战斗数据.战斗流程[#战斗数据.战斗流程].战斗法术 = 名称
					战斗数据.战斗流程[#战斗数据.战斗流程].寄存目标 = 寄存
				elseif 名称=="破釜沉舟" and n == 1 then
					战斗数据.战斗流程[#战斗数据.战斗流程].全屏22 = 名称
					战斗数据.战斗流程[#战斗数据.战斗流程].寄存目标 = 寄存
				elseif 名称=="困兽之斗" and n == 1 then
					local qx = qz(战斗数据.参战单位[编号].气血*0.1)
					-- 确保消耗气血后不会死亡，至少保留1点气血
					if qx >= 战斗数据.参战单位[编号].气血 then
						qx = 战斗数据.参战单位[编号].气血 - 1
						if qx < 1 then
							qx = 1
						end
					end
					战斗数据:消耗气血(编号,qx)
					战斗数据.战斗流程[#战斗数据.战斗流程].前摇掉血 = qx
				end
				战斗数据.战斗流程[#战斗数据.战斗流程].提示={类型="法术",名称=提示内容x,允许=true}
				if n>1 and 重复提示==false then
					战斗数据.战斗流程[#战斗数据.战斗流程].提示.允许=false
				end
				if n==目标数 then
					战斗数据.战斗流程[#战斗数据.战斗流程].返回=true
				elseif 战斗数据:取行动状态(编号)==false then
					战斗数据.战斗流程[#战斗数据.战斗流程].返回=true
					战斗终止=true
				elseif 战斗数据:取目标状态(编号,目标[n+1],1)==false then
					战斗数据.战斗流程[#战斗数据.战斗流程].返回=true
					战斗终止=true
				end
			end
		end
	end
	返回信息 = self:结尾附加(战斗数据,名称,编号,目标,等级,cs,mcjc)
	return 返回信息
end
function 物理技能计算:结尾附加(战斗数据,名称,编号,目标,等级,次数,mcjc)
	local 结尾气血
	local 返回信息
	if mcjc then
		名称 = mcjc
	end
	if 名称=="横扫千军" or 名称=="破釜沉舟" then
		战斗数据:添加状态(名称,战斗数据.参战单位[编号],战斗数据.参战单位[编号],等级)
		if 战斗数据:取经脉(编号, "大唐官府","神凝") and 战斗数据.参战单位[编号].法术状态.干将莫邪~=nil then
		else
			-- 修改为消耗当前气血的10%，而不是最大气血的10%
			结尾气血=qz(战斗数据.参战单位[编号].气血*0.1)
			-- 确保消耗气血后不会死亡，至少保留1点气血
			if 结尾气血 >= 战斗数据.参战单位[编号].气血 then
				结尾气血 = 战斗数据.参战单位[编号].气血 - 1
				if 结尾气血 < 1 then
					结尾气血 = 1
				end
			end
			战斗数据.战斗流程[#战斗数据.战斗流程].结尾死亡=战斗数据:减少气血(编号,结尾气血,编号)
			战斗数据.战斗流程[#战斗数据.战斗流程].结尾气血=结尾气血
			if 战斗数据.参战单位[编号].shenqi.name=="藏锋敛锐" and 取随机数()<=50*战斗数据.参战单位[编号].shenqi.lv+50 then
				战斗计算:添加护盾(战斗数据,编号,结尾气血)
			end
		end
		if 名称=="横扫千军" and 战斗数据.参战单位[编号].类型=="角色" then
			if 战斗数据:取经脉(编号, "大唐官府","破军") then
				战斗数据:添加状态("剑意",战斗数据.参战单位[编号],战斗数据.参战单位[编号],11)
			end
			if 战斗数据:取经脉(编号, "大唐官府","风刃") and 取随机数()<=50 then
				战斗数据:添加状态("风魂", 战斗数据.参战单位[编号], 战斗数据.参战单位[编号],11)
			end
			if 战斗数据:取被动(编号,"连破") then
				local go = false
				local lp = 1
				if 战斗数据.参战单位[编号].勇进~=nil then
					战斗数据.参战单位[编号].勇进=nil
					go=true
				end
				if 战斗数据:取门派是否唯一(编号,"大唐官府") then
					if 战斗数据:取经脉(编号, "大唐官府","孤勇") and 战斗数据:取目标类型(目标[1]) == "召唤兽" and 战斗数据.参战单位[目标[1]].气血<=0 then
						lp = lp + 25
					elseif 战斗数据:取经脉(编号, "大唐官府","熟练") then
						lp = lp + 17
					end
				end
				if 战斗数据.参战单位[编号].亢强~=nil then
					战斗数据.参战单位[编号].亢强 = 0
					if 取随机数() < lp + 战斗数据.参战单位[编号].亢强 then
						go=true
					end
				end
				if 战斗数据.参战单位[编号].新增连破 and 战斗数据.参战单位[编号].新增连破<2 then
					战斗数据.参战单位[编号].新增连破=战斗数据.参战单位[编号].新增连破+1
					go=true
				end
				if go then
					战斗数据.战斗流程[#战斗数据.战斗流程+1]={流程=800,攻击方=编号,挨打方=编号}
					战斗数据:取消状态("横扫千军",战斗数据.参战单位[编号])
				end
			end
		elseif 名称=="破釜沉舟" then
			if 战斗数据:取经脉(编号, "大唐官府","风刃") and 取随机数()<=50 then
				战斗数据:添加状态("风魂", 战斗数据.参战单位[编号], 战斗数据.参战单位[编号],11)
			end
		end
	elseif 名称=="披挂上阵" then
		-- 修改为消耗当前气血的10%，而不是最大气血的10%
		结尾气血=qz(战斗数据.参战单位[编号].气血*0.1)
		-- 确保消耗气血后不会死亡，至少保留1点气血
		if 结尾气血 >= 战斗数据.参战单位[编号].气血 then
			结尾气血 = 战斗数据.参战单位[编号].气血 - 1
			if 结尾气血 < 1 then
				结尾气血 = 1
			end
		end
		战斗数据.战斗流程[#战斗数据.战斗流程].结尾死亡=战斗数据:减少气血(编号,结尾气血,编号)
		战斗数据.战斗流程[#战斗数据.战斗流程].结尾气血=结尾气血
		战斗数据:添加状态("横扫千军",战斗数据.参战单位[编号],战斗数据.参战单位[编号],等级)
		战斗技能:披坚执锐(战斗数据,编号,目标[1],"披挂上阵")
	elseif 名称=="后发制人" then
		战斗数据:取消状态("后发制人",战斗数据.参战单位[编号])
		if 战斗数据:取经脉(编号, "大唐官府","勇武") then
			local 恢复气血 = qz(战斗数据.参战单位[编号].气血上限*0.15)
			if 恢复气血 > 战斗数据.参战单位[编号].等级*8 then
				恢复气血= qz(战斗数据.参战单位[编号].等级*8)
			end
			战斗数据:动画加血流程(编号,恢复气血)
			战斗数据:增加愤怒(编号,10)
			战斗数据.参战单位[目标[1]].受勇武=编号
		else
			结尾气血=qz(战斗数据.参战单位[编号].气血*0.05)
			if 战斗数据.参战单位[编号].气血<=50 then
				结尾气血=1
			end
			战斗数据.战斗流程[#战斗数据.战斗流程].结尾死亡=战斗数据:减少气血(编号,结尾气血,编号)
			战斗数据.战斗流程[#战斗数据.战斗流程].结尾气血=结尾气血
		end
	elseif 名称=="翩鸿一击" then
		战斗数据:添加状态("翩鸿一击",战斗数据.参战单位[编号],战斗数据.参战单位[编号],等级)
		if 战斗数据:取经脉(编号, "大唐官府","翩鸿") then
			local fyjn = 战斗数据:解除状态结果(战斗数据.参战单位[目标[1]], 初始技能计算:取可驱散增益状态())
			if #fyjn > 0 then
				local jczt = fyjn[取随机数(1,#fyjn)]
				战斗数据:取消状态(jczt,战斗数据.参战单位[目标[1]])
			end
		end
	elseif 名称=="棒打雄风" then
		local fyjn = 战斗数据:解除状态结果(战斗数据.参战单位[目标[1]], 初始技能计算:取可驱散增益状态())
		if #fyjn > 0 then
			for i=1,#fyjn do
				table.sort(战斗数据.参战单位[目标[1]].法术状态, function (a,b) return a.回合 < b.回合 end)
				战斗数据:取消状态(fyjn[1],战斗数据.参战单位[目标[1]])
				break
			end
		end
	elseif 名称=="裂石" then
		战斗数据:增加战意(编号,1)
	elseif 名称=="浪涌" then
		if not 战斗数据:取经脉(编号, "凌波城","战诀") then
			战斗数据:增加战意(编号,1)
		end
	elseif 名称=="腾雷" then
		if 取随机数()<=60 then
			战斗数据:添加状态("腾雷",战斗数据.参战单位[目标[1]],战斗数据.参战单位[编号],等级)
		end
		if 战斗数据.参战单位[编号].乘势~=nil then
			战斗数据.参战单位[编号].乘势=nil
			战斗数据:添加状态("强袭",战斗数据.参战单位[编号],战斗数据.参战单位[编号],11)
		end
	elseif 名称=="断岳势" or 名称=="惊涛怒" then
		if 战斗数据.参战单位[编号].法术状态.再战 then
			战斗数据:取消状态("再战",战斗数据.参战单位[编号])
		end
	elseif 名称=="惊涛怒" or 名称=="断岳势" or 名称=="天崩地裂" or 名称=="翻江搅海" then
		if 战斗数据.参战单位[编号].乘势~=nil then
			战斗数据.参战单位[编号].乘势=nil
			战斗数据:添加状态("强袭",战斗数据.参战单位[编号],战斗数据.参战单位[编号],11)
			if 战斗数据:取经脉(编号, "凌波城","追袭") then
				for i=1,次数 do
					if 战斗数据.参战单位[目标[i]] and 战斗数据.参战单位[目标[i]].气血<=0 then
						战斗数据:添加状态("腾雷",战斗数据.参战单位[目标[i]],战斗数据.参战单位[编号],11)
					end
				end
			end
		end
		if 名称=="天崩地裂" or 名称=="翻江搅海" then
			if 战斗数据:取经脉(编号, "凌波城","石摧") and 取随机数()<=32 then
				战斗数据:增加超级战意(编号, 1)
			end
			if 战斗数据:取经脉(编号,"凌波城","山破") and 取随机数() < 64 then
				战斗数据:增加战意(编号, 1)
			end
			if 战斗数据:取经脉(编号,"凌波城","强袭") then
				战斗数据:添加状态("强袭",战斗数据.参战单位[编号],战斗数据.参战单位[编号],11)
			end
		end
	elseif 名称=="天神怒斩" then
		local fyjn = 战斗数据:解除状态结果(战斗数据.参战单位[目标[1]], 初始技能计算:取可驱散增益状态(),1)
		if #fyjn > 0 then
			local jczt = fyjn[取随机数(1,#fyjn)]
			战斗数据:取消状态(jczt,战斗数据.参战单位[目标[1]])
		end
	elseif 名称=="长驱直入" then
	    -- 添加原有的重创效果
	    if 取随机数()<=60 then
	        战斗数据:添加状态("重创",战斗数据.参战单位[目标[1]],战斗数据.参战单位[编号],等级)
	    end

	    -- 添加对指令.附加状态的处理，注意参数顺序：目标在前，攻击方在后
	    if 战斗数据.参战单位[编号].指令 and 战斗数据.参战单位[编号].指令.附加状态 then
	        local 状态名称 = 战斗数据.参战单位[编号].指令.附加状态
	        -- 注意这里参数的顺序：第二个参数是被施法者，第三个参数是施法者
	        战斗数据:添加状态(状态名称, 战斗数据.参战单位[目标[1]], 战斗数据.参战单位[编号], 等级)

	        -- 可以添加调试输出
	        print("天杀星添加附加状态: "..状态名称.." 到目标: "..目标[1])

	        -- 清除指令中的附加状态，避免重复添加
	        战斗数据.参战单位[编号].指令.附加状态 = nil
	    end
	elseif 名称=="摧枯拉朽" then
		战斗数据.参战单位[编号].指令.类型="法术"
		战斗数据.参战单位[编号].指令.参数=战斗数据.参战单位[编号].披坚执锐[战斗数据.参战单位[编号].披坚执锐.可用编号].名称
		战斗数据:法术计算(编号)
	elseif 名称=="天命剑法" then
		结尾气血 = qz(次数*战斗数据.参战单位[编号].最大气血*0.02)
		战斗数据.战斗流程[#战斗数据.战斗流程].结尾死亡=战斗数据:减少气血(编号,结尾气血,编号)
		战斗数据.战斗流程[#战斗数据.战斗流程].结尾气血=结尾气血
		战斗数据:添加状态("天命剑法",战斗数据.参战单位[编号],战斗数据.参战单位[编号],11)
	elseif 名称=="鹰击" or 名称=="疯狂鹰击" then
		战斗数据:添加状态("鹰击",战斗数据.参战单位[编号],战斗数据.参战单位[编号],等级)
		if 战斗数据.参战单位[编号].法术状态.肝胆 and 战斗数据.参战单位[编号].法术状态.肝胆.触发 then
			战斗数据:取消状态("肝胆",战斗数据.参战单位[编号])
		end
	elseif 名称=="象形" then
		战斗数据:添加状态("象形",战斗数据.参战单位[目标[1]],战斗数据.参战单位[编号],等级)
		战斗数据:添加状态("象形（休息）",战斗数据.参战单位[编号],战斗数据.参战单位[编号],等级)
		if 战斗数据:取经脉(编号,"狮驼岭","怒象") and 战斗数据:判定气血百分比(编号,0.3,"气血上限") and 取随机数() <= 70 then
			if 战斗数据.参战单位[编号].法术状态.变身 then
				战斗数据.参战单位[编号].法术状态.变身.回合 = 战斗数据.参战单位[编号].法术状态.变身.回合+2
			end
		else
			if 战斗数据.参战单位[编号].法术状态.狂怒 and 战斗数据.参战单位[编号].法术状态.狂怒.象踏~=nil then
			else
				战斗数据:取消状态("变身",战斗数据.参战单位[编号])
			end
		end
	elseif 名称=="狮搏" then
		if 战斗数据:取目标状态(编号,目标[1],2)==false then
			if 战斗数据:取被动(编号,"狮吼") then
				返回信息="重复狮搏"
			end
		else
			if 战斗数据:取经脉(编号, "狮驼岭","威压") and 30>=取随机数() and 战斗数据:取目标类型(目标[1]) ~= "角色" then
				战斗数据:添加状态("威慑",战斗数据.参战单位[目标[1]],战斗数据.参战单位[编号],11,nil,2)
			end
		end
		if 战斗数据.参战单位[编号].法术状态.狂怒 and 战斗数据.参战单位[编号].法术状态.狂怒.狮噬~=nil and 32>=取随机数() then
			返回信息="重复狮搏"
		end
	elseif 名称=="连环击" then
		战斗数据:添加状态("连环击",战斗数据.参战单位[编号],战斗数据.参战单位[编号],等级)
		if 战斗数据:取经脉(编号, "狮驼岭","乱击") then
			战斗数据.参战单位[编号].法术状态.变身.回合=战斗数据.参战单位[编号].法术状态.变身.回合-2
		elseif 战斗数据.参战单位[编号].法术状态.狂怒 and 战斗数据.参战单位[编号].法术状态.狂怒.雄风~=nil then
		else
			战斗数据:取消状态("变身",战斗数据.参战单位[编号])
		end
		if 战斗数据.参战单位[编号].法术状态.肝胆 and 战斗数据.参战单位[编号].法术状态.肝胆.触发 then
			战斗数据:取消状态("肝胆",战斗数据.参战单位[编号])
		end
	elseif 名称=="满天花雨" then
		战斗数据:添加状态("满天花雨",战斗数据.参战单位[编号],战斗数据.参战单位[编号],69)
		if 战斗数据.参战单位[编号].JM.当前流派=="花间美人" then
			if 战斗数据.参战单位[编号].法术状态.自矜 then
				战斗数据:取消状态("自矜",战斗数据.参战单位[编号])
			else
				local gl = 40
				if 战斗数据:取经脉(编号, "女儿村","汹涌") then
					gl = 55
				end
				if 战斗数据:取指定法宝(编号, "曼陀罗") then
					gl = gl + 战斗数据:取指定法宝境界(编号, "曼陀罗") + 5
				end
				if gl>=取随机数() then
					战斗数据:添加状态("自矜",战斗数据.参战单位[编号],战斗数据.参战单位[编号],69)
					if 战斗数据:取经脉(编号, "女儿村","怒放") then
						战斗数据:增加愤怒(编号,6)
					end
				end
			end
		end
		if 30>=取随机数() and 战斗数据:取封印成功("毒",战斗数据.参战单位[编号].等级,战斗数据.参战单位[编号],战斗数据.参战单位[目标[1]]) then
			战斗数据:添加状态("毒",战斗数据.参战单位[目标[1]],战斗数据.参战单位[编号],战斗数据.参战单位[编号].等级,nil,"满天花雨")
			战斗数据.参战单位[目标[1]].法术状态.毒.回合=3
			if 战斗数据.参战单位[编号].JM.淬芒 then
				战斗数据:添加状态("淬芒",战斗数据.参战单位[编号],战斗数据.参战单位[编号],69)
			elseif 战斗数据.参战单位[编号].JM.天香 then
				战斗数据:增加愤怒(编号,5)
			end
		end
		if 战斗数据.参战单位[编号].花谢花飞~=nil then
			战斗数据.参战单位[编号].花谢花飞=nil
			self:物攻技能计算(编号,名称,11,战斗数据)
		end
	elseif 名称=="葬玉焚花" then
		if 战斗数据.参战单位[编号].法术状态.自矜 then
			战斗数据:取消状态("自矜",战斗数据.参战单位[编号])
		else
			local gl = 40
			if 战斗数据:取经脉(编号, "女儿村","汹涌") then
				gl = 55
			end
			if 战斗数据:取指定法宝(编号, "曼陀罗") then
				gl = gl + 战斗数据:取指定法宝境界(编号, "曼陀罗") + 5
			end
			if gl>=取随机数() then
				战斗数据:添加状态("自矜",战斗数据.参战单位[编号],战斗数据.参战单位[编号],69)
				if 战斗数据:取经脉(编号, "女儿村","怒放") then
					战斗数据:增加愤怒(编号,6)
				end
			end
		end
	elseif 名称 == "蛛丝缠绕" then
		战斗数据:添加状态("天罗地网",战斗数据.参战单位[目标[1]],战斗数据.参战单位[编号],69,nil,4)
	elseif 名称 == "追魂刺" then
		if 战斗数据.参战单位[编号].JM.当前流派=="幽冥巫煞" then
			战斗数据:添加状态("裂魂",战斗数据.参战单位[目标[1]],战斗数据.参战单位[编号],69,nil,4)
		end
	elseif 名称=="烟雨剑法" then
		if 战斗数据:取被动(编号, "骤雨") then
			local sx=3
			if 战斗数据:取经脉(编号,"五庄观","滂沱") then
				sx = 5
			end
			if 战斗数据.参战单位[编号].骤雨.层数<sx then
				战斗数据.参战单位[编号].骤雨.层数=战斗数据.参战单位[编号].骤雨.层数+1
			end
			战斗数据.参战单位[编号].骤雨.回合=3
		end
	elseif 名称=="落土止息" then
		战斗数据.参战单位[编号].人参果={枚=0,回合=4}
		战斗数据:添加状态("落土止息",战斗数据.参战单位[编号],战斗数据.参战单位[编号],69)
	elseif 名称=="威震凌霄" then
		战斗数据:添加状态("威震凌霄",战斗数据.参战单位[目标[1]],战斗数据.参战单位[编号],69)
	elseif 战斗数据.参战单位[编号].法术状态.噬毒~=nil and (名称=="六道无量" or 名称=="百爪狂杀") then
		战斗数据:取消状态("噬毒",战斗数据.参战单位[编号])
	elseif 名称=="剑荡四方" then
		结尾气血=qz(战斗数据.参战单位[编号].气血*0.1)
		-- 确保消耗气血后不会死亡，至少保留1点气血
		if 结尾气血 >= 战斗数据.参战单位[编号].气血 then
			结尾气血 = 战斗数据.参战单位[编号].气血 - 1
			if 结尾气血 < 1 then
				结尾气血 = 1
			end
		end
		战斗数据.战斗流程[#战斗数据.战斗流程].结尾死亡=战斗数据:减少气血(编号,结尾气血,编号)
		战斗数据.战斗流程[#战斗数据.战斗流程].结尾气血=结尾气血
	elseif 名称=="死亡召唤" and 取随机数() <= 10 and 战斗数据.参战单位[目标[1]].法术状态.死亡召唤 == nil then
		战斗数据:添加状态("死亡召唤",战斗数据.参战单位[目标[1]],战斗数据.参战单位[编号],战斗数据.参战单位[编号].等级)
	elseif 名称=="破血狂攻" then
		if 战斗数据:取经脉(编号,"盘丝洞","制怒") and 战斗数据.参战单位[目标[1]].法术状态.天罗地网~=nil then
			战斗数据:增加愤怒(编号,12)
		end
	end
	if 战斗数据.参战单位[编号].法术状态.狂袭 and 战斗数据.参战单位[编号].法术状态.狂袭.死亡 then
		战斗计算:添加掉血(战斗数据,编号,战斗数据.参战单位[编号].气血,编号)
	end


	return 返回信息
end
function 物理技能计算:物攻技能计算1(编号,目标,起始伤害,叠加伤害,返回,允许保护,次数,技能名称,战斗数据)
	战斗数据.战斗流程[#战斗数据.战斗流程+1]={流程=1,攻击方=编号,挨打方={[1]={挨打方=目标,特效={}}}}
	if 装备特技[技能名称]~=nil then
		战斗数据.战斗流程[#战斗数据.战斗流程].特技名称=技能名称
	end
	if 返回 then
		战斗数据.执行等待=战斗数据.执行等待+10
	else
		战斗数据.执行等待=战斗数据.执行等待+5
	end
	local  必杀=false
	local  躲避=false
	local  防御=false
	local  反震=false
	local  必杀几率,暴击倍率 = 0,2
	local  伤害=战斗数据:取基础物理伤害(编号,目标,技能名称)
	伤害 = 伤害*(起始伤害+叠加伤害*(次数-1))

	--这里可以添加状态--这里可以添加状态--这里可以添加状态--这里可以添加目标状态，单次的状态都可以添加在这
	if 技能名称 == "连击" or 技能名称 == "高级连击" then
		if 战斗数据.参战单位[编号].气贯长虹 then
			if 战斗数据.参战单位[编号].连击>=55 then
				伤害 =qz(伤害*1.5)
			else
				伤害 =qz(伤害*1.25)
			end
		end
		if 战斗数据.参战单位[编号].阴伤~=nil and 次数==2 then
			伤害 =伤害 + 战斗数据.参战单位[编号].阴伤
		end
		if 战斗数据.参战单位[编号].碎甲刃~=nil and 20>=取随机数() then
			战斗数据:添加状态("碎甲刃",战斗数据.参战单位[目标],战斗数据.参战单位[编号],战斗数据.参战单位[编号].碎甲刃)
		end
		if 战斗数据.参战单位[编号].毒~=nil and 战斗数据.参战单位[编号].毒 >=取随机数() then
       		战斗数据:添加状态("毒",战斗数据.参战单位[目标],战斗数据.参战单位[编号],战斗数据.参战单位[编号].等级,编号)
    	end
	elseif 技能名称 == "理直气壮" then
		if 战斗数据.参战单位[编号].碎甲刃~=nil and 20>=取随机数() then
			战斗数据:添加状态("碎甲刃",战斗数据.参战单位[目标],战斗数据.参战单位[编号],战斗数据.参战单位[编号].碎甲刃)
		end
	elseif 技能名称 == "剑荡四方" then
	 	if 战斗数据.参战单位[编号].毒~=nil and 战斗数据.参战单位[编号].毒 >=取随机数() then
       		战斗数据:添加状态("毒",战斗数据.参战单位[目标],战斗数据.参战单位[编号],战斗数据.参战单位[编号].等级,编号)
    	end
	elseif 技能名称 == "力劈华山" then
		local cha = 战斗数据.参战单位[编号].伤害 - 战斗数据.参战单位[目标].伤害
		if cha>= 战斗数据.参战单位[编号].等级 * 8 then
			cha = 战斗数据.参战单位[编号].等级 * 8
		end
		伤害 = 伤害 +  cha
		if 战斗数据.参战单位[目标].玉砥柱 then
			伤害 = 伤害 * (1 - 战斗数据.参战单位[目标].玉砥柱)
		end
	elseif 技能名称 == "死亡召唤" or 技能名称 == "惊心一剑" then
		if 战斗数据.参战单位[目标].玉砥柱 then
			伤害 = 伤害 * (1 - 战斗数据.参战单位[目标].玉砥柱)
		end
        if 战斗数据.参战单位[编号].毒~=nil and 战斗数据.参战单位[编号].毒 >=取随机数() then
       	战斗数据:添加状态("毒",战斗数据.参战单位[目标],战斗数据.参战单位[编号],战斗数据.参战单位[编号].等级,编号)
   	    end
	elseif 技能名称 == "后发制人" then
		if 战斗数据:取经脉(编号, "大唐官府", "目空") then
			伤害 = 伤害 + 战斗数据.参战单位[目标].防御 * 0.1
		end
		if 战斗数据:取经脉(编号, "大唐官府", "勇武") then
			伤害 = 伤害 + 战斗数据.参战单位[目标].防御 * 0.4
		end
	elseif 技能名称 == "披挂上阵" then
		伤害 = 伤害*0.4
	elseif 技能名称=="翻江搅海" then
		if 战斗数据:取经脉(编号, "凌波城", "无双") then
			伤害 = 伤害*1.12
		end
	elseif 技能名称 == "天崩地裂" then
		if 战斗数据:取经脉(编号, "凌波城", "无双") then
			必杀几率 = 必杀几率 + 12
		end
		if 战斗数据:取经脉(编号,"凌波城","力战") then
			伤害 = 伤害*1.12
		end
	elseif 技能名称=="天神怒斩" then
		必杀几率 = 必杀几率+25
	elseif 技能名称=="连环击" then
		if 战斗数据:取经脉(编号, "狮驼岭", "乱击") then
			伤害 = 伤害 * 1.1
			伤害 = 伤害 + 战斗数据:取技能等级(编号,"狂兽诀")
		end
	elseif 技能名称=="鹰击" then
		if 战斗数据:取经脉(编号,"狮驼岭","鹰啸") and 战斗数据:取目标类型(目标) == "召唤兽" then
			战斗数据:添加状态("鹰啸",战斗数据.参战单位[目标],战斗数据.参战单位[编号],11)
		end
		if 战斗数据.参战单位[编号].死地~=nil then
			战斗数据.参战单位[编号].死地=2
			伤害 = 伤害 + 200
		end
	elseif 技能名称 == "千蛛噬魂" then
		if 战斗数据.参战单位[目标].法术状态.天罗地网~=nil  then
			伤害 = 伤害 * 1.1
		end
		if 战斗数据:取经脉(编号,"盘丝洞","天网") and sj()<=3 then
			战斗数据:添加状态("天罗地网",战斗数据.参战单位[目标],战斗数据.参战单位[编号],11,nil,4)
		end
		if 战斗数据:取经脉(编号,"盘丝洞","千蛛") and 战斗数据:取目标类型(目标) == "召唤兽" then
			伤害 = 伤害 * 1.12
		end
		if 战斗数据:取经脉(编号, "盘丝洞", "狂击") then
			必杀几率 = 必杀几率 + 4
		end
	elseif 技能名称 == "蛛丝缠绕" then
		if 战斗数据:取经脉(编号,"盘丝洞","引诛") and 战斗数据:取目标类型(目标) == "玩家" then
			伤害 = 伤害 * 1.06
		end
	elseif 名称=="绝命毒牙" then
		if 战斗数据.参战单位[目标].法术状态.天罗地网~=nil  then
			伤害 = 伤害 * (1+战斗数据.参战单位[目标].法术状态.天罗地网.回合*0.1)
		end
	elseif 技能名称=="烟雨剑法" or 技能名称=="飘渺式" then
		local lssh = 0
		if 战斗数据.参战单位[编号].烟雨飘摇 then
			lssh=战斗数据.参战单位[编号].烟雨飘摇
		end
		伤害 = 伤害+lssh
		if 技能名称=="烟雨剑法" and 战斗数据:取经脉(编号, "五庄观", "雨杀") and 次数==2 and 战斗数据.参战单位[目标].类型~="角色" then
			战斗数据.参战单位[编号].雨杀=1
		end
	elseif 技能名称=="敲金击玉" then
		if 战斗数据:取经脉(编号, "五庄观", "刺果") and 战斗数据:取目标类型(目标) == "召唤兽" then
			伤害 = 伤害 *1.2
		end
		if 战斗数据:取经脉(编号, "五庄观", "还元") and 战斗数据.参战单位[编号].人参果.枚>=3 then
			战斗数据:添加状态("还元",战斗数据.参战单位[编号],战斗数据.参战单位[编号],11)
		end
		战斗数据:增加人参果(编号)
	elseif 技能名称=="同伤式" then
		local 速度差 = math.abs(战斗数据.参战单位[编号].速度 - 战斗数据.参战单位[目标].速度)
		if 速度差 < 1 then 速度差 = 0 end
		战斗数据:解除状态结果(战斗数据.参战单位[编号], 初始技能计算:取增益状态法术())
		伤害 = 速度差 * 3 + 伤害
	elseif 技能名称=="天雷斩" then
		local lssh = 0
		if 战斗数据.参战单位[编号].天雷地火 then
			lssh=lssh+战斗数据.参战单位[编号].天雷地火
		end
		if 战斗数据:取指定法宝(编号,"伏魔天书") then
			if 战斗数据:取经脉(编号,"天宫","伏魔") then
				lssh=lssh+qz(战斗数据:取指定法宝境界(编号,"伏魔天书")/2*取随机数(20,30)*1.5)
			else
				lssh=lssh+qz(战斗数据:取指定法宝境界(编号,"伏魔天书")/2*取随机数(20,30))
			end
		end
		if 战斗数据:取经脉(编号,"天宫","疾雷") and not 战斗数据.PK战斗 then
			lssh = lssh + (战斗数据.参战单位[编号].敏捷-战斗数据.参战单位[编号].等级)*0.6
		end
		伤害 = 伤害 + lssh
		if 战斗数据.参战单位[目标].法术状态.天雷斩==nil then
			战斗数据:添加状态("天雷斩",战斗数据.参战单位[目标],战斗数据.参战单位[编号],战斗数据:取技能等级(编号,"天雷斩"))
		end
		if 战斗数据.参战单位[编号].shenqi.name=="裂帛" and 次数 ==1 then
			伤害 = 伤害 * (1+0.08*战斗数据.参战单位[编号].shenqi.lv)
		end
	elseif 技能名称=="风雷斩" then
		local lssh = 0
		if 战斗数据:取指定法宝(编号,"伏魔天书") then
			if 战斗数据:取经脉(编号,"天宫","伏魔") then
				lssh=lssh+qz(战斗数据:取指定法宝境界(编号,"伏魔天书")/2*取随机数(20,30)*1.5)
			else
				lssh=lssh+qz(战斗数据:取指定法宝境界(编号,"伏魔天书")/2*取随机数(20,30))
			end
		end
		if 战斗数据.参战单位[编号].法术状态.雷怒霆激 and 战斗数据.参战单位[编号].法术状态.霹雳弦惊 and 次数==1 then
			伤害 = 伤害 * 1.35
		end
		伤害 = 伤害 + lssh
	elseif 技能名称=="风雷斩·飞霆" then
		local lssh = 0
		if 战斗数据:取指定法宝(编号,"伏魔天书") then
			if 战斗数据:取经脉(编号,"天宫","伏魔") then
				lssh=lssh+qz(战斗数据:取指定法宝境界(编号,"伏魔天书")/2*取随机数(20,30)*1.5)
			else
				lssh=lssh+qz(战斗数据:取指定法宝境界(编号,"伏魔天书")/2*取随机数(20,30))
			end
		end
		伤害 = 伤害 + lssh
	elseif 技能名称=="风雷斩·霹雳" then
		local lssh = 0
		if 战斗数据:取指定法宝(编号,"伏魔天书") then
			if 战斗数据:取经脉(编号,"天宫","伏魔") then
				lssh=lssh+qz(战斗数据:取指定法宝境界(编号,"伏魔天书")/2*取随机数(20,30)*1.5)
			else
				lssh=lssh+qz(战斗数据:取指定法宝境界(编号,"伏魔天书")/2*取随机数(20,30))
			end
		end
		伤害 = 伤害 + lssh
	elseif 技能名称=="六道无量" then
		if 战斗数据.参战单位[目标].类型=="角色" then
			local cha = qz((战斗数据.参战单位[编号].防御-战斗数据.参战单位[目标].防御)*0.75)
			cha = math.abs(cha)
			if cha>战斗数据.参战单位[编号].等级*1.6+70 then
				cha=qz(战斗数据.参战单位[编号].等级*1.6+70)
			end
			战斗数据:添加状态("六道无量",战斗数据.参战单位[目标],战斗数据.参战单位[编号],11,nil,cha)
			战斗数据:添加状态("六道无量",战斗数据.参战单位[编号],战斗数据.参战单位[编号],11,nil,cha)
		end
		if 战斗数据.参战单位[编号].阎罗~=nil and 战斗数据.参战单位[编号].阎罗~=战斗数据.回合数 then
			战斗数据.参战单位[编号].阎罗=nil
			伤害 = 伤害 *1.5
		end
		if 战斗数据.参战单位[编号].法术状态.噬毒~=nil then
			必杀几率 = 必杀几率 + 10
			伤害 = 伤害 * 1.6
		end
	elseif 技能名称=="百爪狂杀" then
		if 战斗数据.参战单位[编号].法术状态.噬毒~=nil then
			伤害 = 伤害 * 1.6
		end
	elseif 技能名称=="百鬼噬魂" then
		local gl = 5
		if 战斗数据:取经脉(编号,"阴曹地府","阴翳") then
			gl = gl +12
		end
		if 战斗数据.参战单位[目标].法术状态.摄魂 and 战斗数据.参战单位[目标].法术状态.摄魂.攻击编号==编号 and 战斗数据:取经脉(编号,"阴曹地府","入魂") then
			gl = gl +50
		end
		if 战斗数据:取经脉(编号,"阴曹地府","蚀骨") and 战斗数据:敌方指定状态数量(编号,"尸腐毒")==0 then
			gl = gl +15
		end
		if sj()<=gl then
			战斗数据:添加状态("尸腐毒",战斗数据.参战单位[目标],战斗数据.参战单位[编号],战斗数据:取技能等级(编号,"百鬼噬魂"))
		end
	elseif 技能名称=="血影蚀心" then
		local gl = 10
		if 战斗数据:取经脉(编号,"阴曹地府","阴翳") then
			gl = gl +12
		end
		if 战斗数据.参战单位[目标].法术状态.摄魂 and 战斗数据.参战单位[目标].法术状态.摄魂.攻击编号==编号 and 战斗数据:取经脉(编号,"阴曹地府","入魂") then
			gl = gl +50
		end
		if 战斗数据:取经脉(编号,"阴曹地府","蚀骨") and 战斗数据:敌方指定状态数量(编号,"尸腐毒")==0 then
			gl = gl +15
		end
		if sj()<=gl then
			战斗数据:添加状态("尸腐毒",战斗数据.参战单位[目标],战斗数据.参战单位[编号],战斗数据:取技能等级(编号,"百鬼噬魂"))
		end
	elseif 技能名称=="无赦咒令" then
		local lszy = 战斗数据:解除状态结果(战斗数据.参战单位[目标],初始技能计算:取可驱散增益状态(), 1)
		if #lszy>0 then
			local jczt = lszy[取随机数(1,#lszy)]
			战斗数据:取消状态(jczt, 战斗数据.参战单位[目标])
		end
		if 战斗数据.参战单位[目标].法术状态.尸腐毒 or 战斗数据.参战单位[目标].法术状态.毒 then
			local lszy = 战斗数据:解除状态结果(战斗数据.参战单位[目标],初始技能计算:取可驱散增益状态(), 1)
			if #lszy>0 then
				local jczt = lszy[取随机数(1,#lszy)]
				战斗数据:取消状态(jczt, 战斗数据.参战单位[目标])
			end
		end
	elseif 技能名称=="自矜" then
		local xs = 0.6
		if 战斗数据:取经脉(编号, "女儿村","花刺") then
			xs = 0.8
		end
		if 战斗数据.参战单位[目标].法术状态.毒 and 战斗数据.参战单位[目标].法术状态.门派=="女儿村" then
			xs = xs + 0.12
		end
		if 战斗数据:取经脉(编号, "女儿村","乘胜") and 战斗数据.参战单位[目标].气血<=战斗数据.参战单位[目标].最大气血*0.3 then
			xs = xs + 0.1
		end
		伤害 = 伤害 * xs
	elseif 技能名称=="生杀予夺" and 战斗数据.参战单位[目标].法术状态.尸腐毒 then
		暴击倍率 =3.2
	elseif 技能名称=="水击三千" then
		伤害=伤害+战斗数据.参战单位[编号].等级
		暴击倍率 =2.6
		if 战斗数据.参战单位[编号].唯一鲲鹏~=nil then
			伤害 = 伤害 *1.5
		end
	end
	伤害=qz(伤害)
	local  最终伤害=战斗数据:取最终物理伤害(编号,目标,伤害,必杀几率,暴击倍率,技能名称)
	local  保护=false
	local  保护编号=0
	local  天神护法结果 = 战斗数据:取天神护法保护(目标)

-- 检查天神护法结果
	if 天神护法结果 then
	    保护编号 = 天神护法结果[3]  -- 第三个值是保护编号
	    保护 = true
	    战斗数据.执行等待 = 战斗数据.执行等待 + 5
	end

	if 战斗数据.参战单位[目标].类型 == "角色" and 战斗数据.参战单位[目标].气血 < 战斗数据.参战单位[目标].最大气血*0.5 and 最终伤害.伤害*0.3 < 战斗数据.参战单位[目标].气血 and 最终伤害.伤害 > 战斗数据.参战单位[目标].最大气血*0.1  and 取随机数() < 70 and 允许保护 then
		local 随机保护 = {}
		for n=1,#战斗数据.参战单位 do
			if 保护编号==0 and 战斗数据:取行动状态(n) and 战斗数据.参战单位[目标].法术状态.惊魂掌==nil and 战斗数据.参战单位[n].队伍==战斗数据.参战单位[目标].队伍 and 战斗数据.参战单位[n].类型 == "角色" and n ~= 目标 then
				table.insert(随机保护,n)
			end
		end
		if #随机保护 >= 1 then
			保护编号 = 随机保护[取随机数(1,#随机保护)]
			保护=true
			战斗数据.执行等待=战斗数据.执行等待+5
		end
	else
		-- 添加夜叉互相保护的判断
		if 战斗数据.参战单位[目标].名称 == "夜叉" and 允许保护 then
			for n=1,#战斗数据.参战单位 do
				if 保护编号==0 and 战斗数据:取行动状态(n) and 战斗数据.参战单位[n].名称 == "夜叉" and 战斗数据.参战单位[n].队伍==战斗数据.参战单位[目标].队伍 and n ~= 目标 then
					if 取随机数() <= 100 then  -- 100%概率触发保护
						保护编号=n
						保护=true
						战斗数据.执行等待=战斗数据.执行等待+3
						break
					end
				end
			end
		end

		-- 原有的保护判断
		for n=1,#战斗数据.参战单位 do
			if 保护编号==0 and 战斗数据:取行动状态(n) and 战斗数据.参战单位[目标].法术状态.惊魂掌==nil and 战斗数据.参战单位[n].指令 ~= nil and 战斗数据.参战单位[n].指令.类型=="保护" and  战斗数据.参战单位[n].队伍==战斗数据.参战单位[目标].队伍 and  战斗数据.参战单位[n].指令.目标==目标 and 允许保护 then
				保护编号=n
				保护=true
				战斗数据.参战单位[n].指令.类型=""
				战斗数据.执行等待=战斗数据.执行等待+3
			end
		end
	end
	if 战斗数据.参战单位[目标].指令 ~= nil and 战斗数据.参战单位[目标].指令.类型 == "防御" and 战斗数据:取行动状态(目标) and 战斗数据:取可否防御(目标) then
		if 技能名称=="壁垒击破" then
			最终伤害.伤害 = 最终伤害.伤害 * 1.2
		else
			最终伤害.伤害 = 最终伤害.伤害 * 0.5
		end
		最终伤害.动作 = "防御"
		防御 = true
		if 战斗数据.参战单位[目标].点石成金 then
			最终伤害.伤害=最终伤害.伤害*战斗数据.参战单位[目标].点石成金
		end
		if 战斗数据.参战单位[目标].法术状态.愈勇 then
			战斗数据:取消状态("愈勇",战斗数据.参战单位[目标])
		end
	end
	local  结果 = 战斗数据:取伤害结果(编号,目标,最终伤害.伤害,最终伤害.暴击,保护,2,技能名称,防御)
	if 战斗数据:取目标类型(编号) == "召唤兽" and 技能名称 then
		if 战斗数据.友方经脉统计[战斗数据.参战单位[编号].队伍]~=nil and 战斗数据.友方经脉统计[战斗数据.参战单位[编号].队伍].出其不意~=nil then
			if 战斗数据.友方经脉统计[战斗数据.参战单位[编号].队伍].出其不意.回合~=战斗数据.回合数 then
				战斗数据.友方经脉统计[战斗数据.参战单位[编号].队伍].出其不意={技能={},回合=战斗数据.回合数}
				if 战斗数据.参战单位[编号].出其不意~=nil then
					结果.伤害=qz(结果.伤害*1.15)
				end
			else
				if 战斗数据.参战单位[编号].出其不意~=nil then
					local go = true
					for k,v in pairs(战斗数据.友方经脉统计[战斗数据.参战单位[编号].队伍].出其不意.技能) do
						if v==技能名称 then
							go = false
							break
						end
					end
					if go then
						结果.伤害=qz(结果.伤害*1.15)
					end
				end
			end
			战斗数据.友方经脉统计[战斗数据.参战单位[编号].队伍].出其不意.技能[#战斗数据.友方经脉统计[战斗数据.参战单位[编号].队伍].出其不意.技能+1]=技能名称
		end
	end
	if 战斗数据.参战单位[目标].共生 ~= nil then
		战斗技能:物理共生计算(战斗数据,编号,目标,qz(结果.伤害+1),最终伤害.动作,结果.特效,返回)
		return
	end
	if 技能名称=="横扫千军" and 战斗数据.参战单位[编号].队伍~=0 then
		if not 战斗数据.PK战斗 and 战斗数据.参战单位[编号].jianshe and 战斗数据:取经脉(编号,"大唐官府", "扶阵") then
			local 目标数 = 3
			local 溅射目标=战斗数据:取多个溅射目标(编号,目标,目标数+1)
			for i=1,#溅射目标 do
				if 溅射目标[i] ~= 目标 and 目标数~=0 then
					战斗计算:添加掉血(战斗数据,溅射目标[i],结果.伤害* 战斗数据.参战单位[编号].jianshe,编号)
				end
			end
		else
			if 战斗数据.参战单位[编号].法术状态["剑意"] and 战斗数据.参战单位[编号].法术状态["剑意"].层数>=11 then
				保护=false
				local 目标数 = 3
				local 溅射目标=战斗数据:取多个溅射目标(编号,目标,目标数+1)
				for i=1,#溅射目标 do
					if 溅射目标[i] ~= 目标 and 目标数~=0 then
						战斗计算:添加掉血(战斗数据,溅射目标[i],结果.伤害*0.45,编号)
					end
				end
			end
		end
	elseif 名称=="长驱直入" or 名称=="泼天乱棒" then
		保护=false
	end
	if 保护 then
		local x1 = 0.7
		local x2 = 0.3
		if 战斗数据.参战单位[编号].势如破竹 then
			x1 = 0.4
			x2 = 0.6
		end
		if 战斗数据.参战单位[保护编号].阴阳护 then
			x1 = x1 - 战斗数据.参战单位[保护编号].阴阳护
		end
		local 保护伤害=math.floor(结果.伤害*(x1))
		if 保护伤害<1 then 保护伤害=1 end
		local 保护死亡=战斗数据:减少气血(保护编号,保护伤害)
		结果.伤害=math.floor(结果.伤害*(x2))
		if 技能名称=="横扫千军" and 战斗数据.参战单位[编号].法术状态["剑意"] and 战斗数据.参战单位[编号].法术状态["剑意"].层数>=2 then
			结果.伤害=math.floor(结果.伤害*1.05)
		end
		if 战斗数据:取经脉(编号,"大唐官府", "破空") then
			结果.伤害=math.floor(结果.伤害*1.1)
		end
		战斗数据.战斗流程[#战斗数据.战斗流程].保护数据={编号=保护编号,伤害=保护伤害,死亡=保护死亡}
	end
	if 技能名称=="善恶有报" then
		if 取随机数()<=30 then
			结果.类型=2
			结果.伤害=math.floor(结果.伤害*0.25)
			if 结果.伤害<1 then
				结果.伤害=1
			end
			if 战斗数据.参战单位[目标].气血==战斗数据.参战单位[目标].最大气血 then
				战斗数据.战斗流程[#战斗数据.战斗流程].假动作 = true
				战斗数据.战斗流程[#战斗数据.战斗流程].返回 = true
				return
			end
		elseif 结果.类型~=2 then
			结果.伤害=qz(结果.伤害*1.5)
			if 战斗数据.参战单位[目标].玉砥柱 then
				结果.伤害 = 结果.伤害 * (1 - 战斗数据.参战单位[目标].玉砥柱)
			end
		end
	end
	结果.伤害=qz(结果.伤害)
	if 结果.伤害<1 or 战斗数据.参战单位[目标].物理免疫 then
		结果.伤害=1
	end
	if 结果.类型==2 then
		战斗数据:增加气血(目标,结果.伤害)
	else
		战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].死亡=战斗数据:减少气血(目标,结果.伤害,编号,技能名称)
		战斗技能:凝光炼彩(战斗数据,目标,结果.伤害)
		战斗计算:物理技能掉血计算(战斗数据,编号,结果.伤害,目标,技能名称)
	end
	战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].动作=最终伤害.动作

	战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].特效 = {}

	for i=1,#结果.特效 do
		table.insert(战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].特效,结果.特效[i])
	end
	if 技能名称~="后发制人" then
		战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].特效[#战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].特效+1]=技能名称
	end
	if 技能名称=="泼天乱棒" and 战斗数据:取目标状态(编号,目标,1) then
		local 目标数 = 3
		local 溅射目标=战斗数据:取多个溅射目标(编号,目标,目标数+1)
		for i=1,#溅射目标 do
			if 溅射目标[i] ~= 目标 and 目标数~=0 then
				战斗计算:添加掉血(战斗数据,溅射目标[i],最终伤害.伤害*0.3,编号)
			end
		end
	end
	local 吸血=战斗数据.参战单位[编号].吸血
	if 吸血==nil and 战斗数据.参战单位[编号].法术状态.移魂化骨 then
		吸血=战斗数据.参战单位[编号].法术状态.移魂化骨.吸血
	end
	if 吸血~=nil and 战斗数据.参战单位[目标].鬼魂==nil  then
		local 吸血伤害=math.floor(结果.伤害*吸血)
		if 吸血伤害<1 then
			吸血伤害=1
		end
		战斗数据:增加气血(编号,吸血伤害)
		战斗数据.战斗流程[#战斗数据.战斗流程].吸血伤害=吸血伤害
		if 吸血<0.3 and 战斗数据.参战单位[编号].法术状态.毒==nil and 初始技能计算:取吸血异常(战斗数据,目标) then
			战斗数据:添加状态("毒", 战斗数据.参战单位[编号],战斗数据.参战单位[目标], 11)
		end
	else
		if 战斗数据:取经脉(编号,"狮驼岭","化血") and 取随机数() <=30 then
			local 吸血伤害=math.floor(结果.伤害*0.16)
			if 吸血伤害<1 then
				吸血伤害=1
			end
			战斗数据:增加气血(编号,math.floor(吸血伤害))
			战斗数据.战斗流程[#战斗数据.战斗流程].吸血伤害=math.floor(吸血伤害)
			if 战斗数据.参战单位[编号].法术状态.毒==nil and 初始技能计算:取吸血异常(战斗数据,目标) then
				战斗数据:添加状态("毒", 战斗数据.参战单位[编号],战斗数据.参战单位[目标], 11)
			end
		elseif 战斗数据.参战单位[编号].法术状态.狂怒 and 战斗数据.参战单位[编号].法术状态.狂怒.攫取~=nil and 取随机数() <=50 then
			local 吸血伤害=math.floor(结果.伤害*0.16)
			if 吸血伤害<1 then
				吸血伤害=1
			end
			战斗数据:增加气血(编号,吸血伤害)
			战斗数据.战斗流程[#战斗数据.战斗流程].吸血伤害=吸血伤害
			if 战斗数据.参战单位[编号].法术状态.毒==nil and 初始技能计算:取吸血异常(战斗数据,目标) then
				战斗数据:添加状态("毒", 战斗数据.参战单位[编号],战斗数据.参战单位[目标], 11)
			end
		end
	end
	local 反震=战斗数据.参战单位[目标].反震
	if 结果.伤害>=4 and 战斗数据.参战单位[编号].偷袭==nil and 反震~=nil and 取随机数()<=30 and 保护==false then
		local 反震伤害=math.floor(结果.伤害*反震)
		if 战斗数据.参战单位[目标].坚甲~=nil then
			反震伤害= 反震伤害 + 战斗数据.参战单位[目标].坚甲
		end
		if 战斗数据:取经脉(编号,"狮驼岭","狂袭") then
			local sh = qz(反震伤害*0.04)
			战斗数据:添加状态("狂袭",战斗数据.参战单位[编号],战斗数据.参战单位[编号],sh)
		end
		战斗数据.战斗流程[#战斗数据.战斗流程].反震伤害=反震伤害
		战斗数据.战斗流程[#战斗数据.战斗流程].反震死亡=战斗数据:减少气血(编号,反震伤害,目标)
		战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].特效[#战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].特效+1]="反震"
		if 战斗数据.参战单位[编号].法术状态.狂袭 and 战斗数据.参战单位[编号].气血<=0 then
			战斗数据.参战单位[编号].气血=1
			战斗数据.战斗流程[#战斗数据.战斗流程].反震死亡 = false
			战斗数据.参战单位[编号].法术状态.狂袭.死亡=true
		end
		战斗数据.执行等待=战斗数据.执行等待+3
	elseif 结果.伤害>=4 and 战斗数据.参战单位[目标].法术状态.修罗咒 then
		local 反震伤害=math.floor(结果.伤害*0.5)
		if 反震伤害>=战斗数据.参战单位[目标].等级*8 then
			反震伤害=战斗数据.参战单位[目标].等级*8
		end
		战斗数据.战斗流程[#战斗数据.战斗流程].反震伤害=反震伤害
		战斗数据.战斗流程[#战斗数据.战斗流程].反震死亡=战斗数据:减少气血(编号,反震伤害,目标)
		战斗数据.执行等待=战斗数据.执行等待+2
	end
	战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].伤害=结果.伤害
	战斗数据.战斗流程[#战斗数据.战斗流程].挨打方[1].伤害类型=结果.类型
	战斗数据.战斗流程[#战斗数据.战斗流程].返回=返回
end
return 物理技能计算